package com.yxt.order.assistant.server.mcp;


import com.yxt.domain.order.mcp.req.QueryOrderMakeNoReq;
import com.yxt.domain.order.mcp.res.QueryOrderMakeNoRes;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.atom.sdk.mcp.req.OrderDetailQueryReq;
import com.yxt.order.atom.sdk.mcp.req.OrderTypeQueryReq;
import com.yxt.order.atom.sdk.mcp.res.OrderTypeQueryRes;
import com.yxt.order.atom.sdk.online_order.order_info.dto.res.FullOrderDtoResDto;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 订单查询端点
 */
public interface OrderQueryEndPoint {

  String BASE_PATH = "/mcp/query";

  /**
   * 查询订单类型
   *
   * @param orderTypeReq
   * @return
   */
  @PostMapping(BASE_PATH + "/order-type")
  ResponseBase<OrderTypeQueryRes> queryOrderType(@RequestBody OrderTypeQueryReq orderTypeReq);


  /**
   * 查询订单详情
   *
   * @param orderDetailQueryReq
   * @return
   */
  @PostMapping(BASE_PATH + "/order-detail")
  ResponseBase<FullOrderDtoResDto> queryOrderDetail(@RequestBody OrderDetailQueryReq orderDetailQueryReq);

  /**
   * 查询订单批号
   *
   * @param queryOrderMakeNoReq
   * @return
   */
  @PostMapping(BASE_PATH + "/order-make-no")
  ResponseBase<QueryOrderMakeNoRes> queryOrderMakeNo(@RequestBody QueryOrderMakeNoReq queryOrderMakeNoReq);



}
