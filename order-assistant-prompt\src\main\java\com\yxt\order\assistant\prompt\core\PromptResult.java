package com.yxt.order.assistant.prompt.core;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 提示词构建结果
 * 包含构建后的提示词内容和相关元数据
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public class PromptResult {
    
    private final String content;
    private final List<PromptComponent> components;
    private final PromptContext context;
    private final LocalDateTime buildTime;
    private final Map<String, Object> metadata;
    private final boolean success;
    private final List<String> warnings;
    private final Optional<Exception> error;
    
    private PromptResult(Builder builder) {
        this.content = builder.content;
        this.components = builder.components;
        this.context = builder.context;
        this.buildTime = builder.buildTime;
        this.metadata = builder.metadata;
        this.success = builder.success;
        this.warnings = builder.warnings;
        this.error = builder.error;
    }
    
    /**
     * 获取构建后的提示词内容
     * 
     * @return 提示词内容
     */
    public String getContent() {
        return content;
    }
    
    /**
     * 获取使用的组件列表
     * 
     * @return 组件列表
     */
    public List<PromptComponent> getComponents() {
        return components;
    }
    
    /**
     * 获取构建时的上下文
     * 
     * @return 上下文实例
     */
    public PromptContext getContext() {
        return context;
    }
    
    /**
     * 获取构建时间
     * 
     * @return 构建时间
     */
    public LocalDateTime getBuildTime() {
        return buildTime;
    }
    
    /**
     * 获取元数据
     * 
     * @return 元数据映射
     */
    public Map<String, Object> getMetadata() {
        return metadata;
    }
    
    /**
     * 检查构建是否成功
     * 
     * @return true表示成功，false表示失败
     */
    public boolean isSuccess() {
        return success;
    }
    
    /**
     * 获取警告信息
     * 
     * @return 警告信息列表
     */
    public List<String> getWarnings() {
        return warnings;
    }
    
    /**
     * 获取错误信息
     * 
     * @return 错误信息的Optional包装
     */
    public Optional<Exception> getError() {
        return error;
    }
    
    /**
     * 获取指定的元数据
     * 
     * @param key 元数据键
     * @return 元数据值的Optional包装
     */
    public Optional<Object> getMetadata(String key) {
        return Optional.ofNullable(metadata.get(key));
    }
    
    /**
     * 获取指定类型的元数据
     * 
     * @param key 元数据键
     * @param type 元数据类型
     * @param <T> 元数据类型
     * @return 元数据值的Optional包装
     */
    @SuppressWarnings("unchecked")
    public <T> Optional<T> getMetadata(String key, Class<T> type) {
        Object value = metadata.get(key);
        if (value != null && type.isInstance(value)) {
            return Optional.of((T) value);
        }
        return Optional.empty();
    }
    
    /**
     * 创建构建器
     * 
     * @return 构建器实例
     */
    public static Builder builder() {
        return new Builder();
    }
    
    /**
     * 构建器类
     */
    public static class Builder {
        private String content;
        private List<PromptComponent> components;
        private PromptContext context;
        private LocalDateTime buildTime = LocalDateTime.now();
        private Map<String, Object> metadata = new java.util.HashMap<>();
        private boolean success = true;
        private List<String> warnings = new java.util.ArrayList<>();
        private Optional<Exception> error = Optional.empty();
        
        public Builder content(String content) {
            this.content = content;
            return this;
        }
        
        public Builder components(List<PromptComponent> components) {
            this.components = components;
            return this;
        }
        
        public Builder context(PromptContext context) {
            this.context = context;
            return this;
        }
        
        public Builder buildTime(LocalDateTime buildTime) {
            this.buildTime = buildTime;
            return this;
        }
        
        public Builder metadata(Map<String, Object> metadata) {
            this.metadata = metadata;
            return this;
        }
        
        public Builder addMetadata(String key, Object value) {
            this.metadata.put(key, value);
            return this;
        }
        
        public Builder success(boolean success) {
            this.success = success;
            return this;
        }
        
        public Builder warnings(List<String> warnings) {
            this.warnings = warnings;
            return this;
        }
        
        public Builder addWarning(String warning) {
            this.warnings.add(warning);
            return this;
        }
        
        public Builder error(Exception error) {
            this.error = Optional.ofNullable(error);
            this.success = error == null;
            return this;
        }
        
        public PromptResult build() {
            return new PromptResult(this);
        }
    }
}
