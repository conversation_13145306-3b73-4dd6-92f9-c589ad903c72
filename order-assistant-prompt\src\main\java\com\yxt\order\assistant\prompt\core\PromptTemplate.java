package com.yxt.order.assistant.prompt.core;

import java.util.Map;

/**
 * 提示词模板接口
 * 支持模板化的提示词构建
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public interface PromptTemplate {
    
    /**
     * 渲染模板
     * 
     * @param templateContent 模板内容
     * @param variables 变量映射
     * @return 渲染后的内容
     */
    String render(String templateContent, Map<String, Object> variables);
    
    /**
     * 渲染模板文件
     * 
     * @param templateName 模板名称
     * @param variables 变量映射
     * @return 渲染后的内容
     */
    String renderTemplate(String templateName, Map<String, Object> variables);
    
    /**
     * 检查模板是否存在
     * 
     * @param templateName 模板名称
     * @return true表示存在，false表示不存在
     */
    boolean templateExists(String templateName);
    
    /**
     * 获取模板引擎名称
     * 
     * @return 模板引擎名称
     */
    String getEngineName();
    
    /**
     * 验证模板语法
     * 
     * @param templateContent 模板内容
     * @return 验证结果
     */
    default TemplateValidationResult validateTemplate(String templateContent) {
        try {
            render(templateContent, java.util.Collections.emptyMap());
            return TemplateValidationResult.success();
        } catch (Exception e) {
            return TemplateValidationResult.failure(e.getMessage());
        }
    }
    
    /**
     * 模板验证结果
     */
    class TemplateValidationResult {
        private final boolean valid;
        private final String errorMessage;
        
        private TemplateValidationResult(boolean valid, String errorMessage) {
            this.valid = valid;
            this.errorMessage = errorMessage;
        }
        
        public static TemplateValidationResult success() {
            return new TemplateValidationResult(true, null);
        }
        
        public static TemplateValidationResult failure(String errorMessage) {
            return new TemplateValidationResult(false, errorMessage);
        }
        
        public boolean isValid() {
            return valid;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
    }
}
