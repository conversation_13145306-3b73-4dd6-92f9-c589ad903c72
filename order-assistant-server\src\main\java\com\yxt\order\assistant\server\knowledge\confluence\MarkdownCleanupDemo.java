package com.yxt.order.assistant.server.knowledge.confluence;

import java.lang.reflect.Method;

/**
 * Markdown清理功能演示
 * 展示如何移除空行和空标题
 */
public class MarkdownCleanupDemo {

    public static void main(String[] args) {
        System.out.println("=== Markdown清理功能演示 ===\n");
        
        try {
            ConfluenceMarkdownExporter exporter = new ConfluenceMarkdownExporter();
            
            // 使用反射获取私有方法
            Method cleanupMarkdownMethod = ConfluenceMarkdownExporter.class.getDeclaredMethod("cleanupMarkdown", String.class);
            cleanupMarkdownMethod.setAccessible(true);
            
            // 演示案例1: 合并连续空行
            System.out.println("📝 案例1: 合并连续空行（保留单个空行）");
            String input1 = "# 标题1\n\n\n内容1\n\n\n\n## 标题2\n\n内容2\n\n\n";
            System.out.println("输入:");
            System.out.println("```");
            System.out.println(input1.replace("\n", "\\n\n"));
            System.out.println("```");

            String result1 = (String) cleanupMarkdownMethod.invoke(exporter, input1);
            System.out.println("输出:");
            System.out.println("```");
            System.out.println(result1.replace("\n", "\\n\n"));
            System.out.println("```\n");
            
            // 演示案例2: 移除空标题
            System.out.println("📝 案例2: 移除空标题");
            String input2 = "# 主标题\n## 空标题1\n### 空标题2\n## 有内容的标题\n这是内容\n### 另一个空标题\n## 最后的标题\n最后的内容";
            System.out.println("输入:");
            System.out.println("```");
            System.out.println(input2.replace("\n", "\\n\n"));
            System.out.println("```");
            
            String result2 = (String) cleanupMarkdownMethod.invoke(exporter, input2);
            System.out.println("输出:");
            System.out.println("```");
            System.out.println(result2.replace("\n", "\\n\n"));
            System.out.println("```\n");
            
            // 演示案例3: 复杂情况
            System.out.println("📝 案例3: 复杂情况（同时合并连续空行和移除空标题）");
            String input3 = "# 主标题\n\n\n## 空标题1\n\n\n### 空标题2\n\n\n## 有内容的标题\n\n\n这是一些内容\n\n\n还有更多内容\n\n\n### 子标题\n\n\n子标题内容\n\n\n## 另一个空标题\n\n\n# 最终标题\n\n\n最终内容\n\n\n";
            System.out.println("输入:");
            System.out.println("```");
            System.out.println(input3.replace("\n", "\\n\n"));
            System.out.println("```");
            
            String result3 = (String) cleanupMarkdownMethod.invoke(exporter, input3);
            System.out.println("输出:");
            System.out.println("```");
            System.out.println(result3.replace("\n", "\\n\n"));
            System.out.println("```\n");
            
            // 演示案例4: 只有标题的情况
            System.out.println("📝 案例4: 只有标题（全部移除）");
            String input4 = "# 标题1\n## 标题2\n### 标题3";
            System.out.println("输入:");
            System.out.println("```");
            System.out.println(input4.replace("\n", "\\n\n"));
            System.out.println("```");
            
            String result4 = (String) cleanupMarkdownMethod.invoke(exporter, input4);
            System.out.println("输出:");
            System.out.println("```");
            System.out.println(result4.isEmpty() ? "(空字符串)" : result4.replace("\n", "\\n\n"));
            System.out.println("```\n");
            
            System.out.println("✅ 演示完成！");
            System.out.println("\n📋 功能总结:");
            System.out.println("1. ✅ 合并连续空行为单个空行（保持markdown格式）");
            System.out.println("2. ✅ 移除没有内容的标题（任何级别的标题）");
            System.out.println("3. ✅ 保持适当的行间距，确保markdown正确显示");
            System.out.println("4. ✅ 处理复杂的嵌套情况");
            
        } catch (Exception e) {
            System.err.println("❌ 演示失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
