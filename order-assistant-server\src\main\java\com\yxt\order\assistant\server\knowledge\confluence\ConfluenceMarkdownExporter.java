package com.yxt.order.assistant.server.knowledge.confluence;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.yxt.order.assistant.server.config.OrderAssistantConfig;
import com.yxt.order.assistant.server.knowledge.confluence.dto.ConfluenceChildrenResponse;
import com.yxt.order.assistant.server.knowledge.confluence.dto.ConfluencePage;
import com.yxt.order.assistant.server.knowledge.service.KnowledgeService;
import com.yxt.order.assistant.server.repository.KnowledgeRepository;
import com.yxt.order.assistant.server.repository.entity.KnowledgeBase.CfConfig;
import com.yxt.order.assistant.server.repository.enums.KnowledgeBaseSource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStreamWriter;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Queue;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.Node;
import org.jsoup.nodes.TextNode;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * AI 生成的
 */
@Component
@Slf4j
public class ConfluenceMarkdownExporter {

  @Resource
  private OrderAssistantConfig orderAssistantConfig;


  @Resource
  private KnowledgeService knowledgeService;

  private final String TITLE_EMPTY = "";
  private final String BODY_EMPTY = "";

  private OkHttpClient client;
  private Gson gson;

  private String outputDir;
  private String resourcesDir;


  @Value("${confluence.url:https://yxtcf.hxyxt.com}")
  private String confluenceUrl;
  @Value("${confluence.token:NDUxMjY5MDIyMDcwOigCdFHosMopCmWokCv3Cz+4jQOV}")
  private String personalToken;

  @PostConstruct
  public void init() {
    this.outputDir = "confluence-export";
    this.resourcesDir = outputDir + File.separator + "resources";

    // 配置OkHttp客户端
    this.client = new OkHttpClient.Builder().connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(60, TimeUnit.SECONDS).writeTimeout(60, TimeUnit.SECONDS).build();

    // 配置Gson
    this.gson = new GsonBuilder().setPrettyPrinting().create();

    // 创建输出目录
    if (orderAssistantConfig.getCfPullWriteFile()) {
      createOutputDirectories();
    }
  }

  /**
   * 创建输出目录
   */
  private void createOutputDirectories() {
    File dir = new File(outputDir);
    if (!dir.exists()) {
      dir.mkdirs();
    }

    File resourceDir = new File(resourcesDir);
    if (!resourceDir.exists()) {
      resourceDir.mkdirs();
    }
  }

  /**
   * 导出页面及其所有子页面到Markdown 使用广度优先遍历避免递归调用栈溢出
   */
  public void pullPageAndChildrenToMarkdown(Long knowledgeBaseId, CfConfig cfConfig) {
    String rootPageId = cfConfig.getRootPageId();

    Queue<String> pageQueue = new LinkedList<>();
    Set<String> processedPages = new HashSet<>();
    List<String> exportedFiles = new ArrayList<>();

    pageQueue.offer(rootPageId);

    log.info("🚀 开始导出Confluence页面，根页面ID: {}", rootPageId);

    while (!pageQueue.isEmpty()) {
      String pageId = pageQueue.poll();

      if (processedPages.contains(pageId)) {
        continue;
      }

      processedPages.add(pageId);

      try {
        // 导出当前页面
        String exportedFile = exportPageToMarkdown(knowledgeBaseId, pageId);
        if (exportedFile != null) {
          exportedFiles.add(exportedFile);
        }

        // 获取子页面并加入队列
        List<String> childPageIds = getChildPageIds(pageId);
        pageQueue.addAll(childPageIds);

      } catch (Exception e) {
        log.warn("❌处理页面失败 {}: {}", pageId, e.getMessage());
        e.printStackTrace();
      }
    }

    log.info("✅ 导出完成！共导出 {} 个页面", exportedFiles.size());
    log.info("📁 输出目录: {}", new File(outputDir).getAbsolutePath());

    // 生成索引文件
    if (orderAssistantConfig.getCfPullWriteFile()) {
      generateIndexFile(exportedFiles);
    }
  }

  /**
   * 获取子页面ID列表
   */
  private List<String> getChildPageIds(String pageId) {
    List<String> childPageIds = new ArrayList<>();
    String childrenUrl = confluenceUrl + "/rest/api/content/" + pageId + "/child/page?limit=100";

    Request request = new Request.Builder().url(childrenUrl)
        .addHeader("Authorization", "Bearer " + personalToken)
        .addHeader("Content-Type", "application/json").build();

    try (Response response = client.newCall(request).execute()) {
      if (!response.isSuccessful()) {
        log.warn("❌ 获取子页面失败 {}: {} {}", pageId, response.code(), response.message());
        return childPageIds;
      }

      String json = response.body().string();
      ConfluenceChildrenResponse childrenResponse = gson.fromJson(json,
          ConfluenceChildrenResponse.class);

      if (childrenResponse.getResults() != null) {
        for (ConfluenceChildrenResponse.ConfluencePageSummary page : childrenResponse.getResults()) {
          if ("page".equals(page.getType()) && "current".equals(page.getStatus())) {
            String title = page.getTitle();

            if (orderAssistantConfig.getCfBlackListConfig()
                .isBlackList(page.getId(), page.getTitle())) {
              continue;
            }

            childPageIds.add(page.getId());
            log.info("📄 发现子页面: {} (ID: {})", title, page.getId());
          }
        }
      }

    } catch (Exception e) {
      log.warn("❌ 解析子页面响应失败 {}: {}", pageId, e.getMessage());
      e.printStackTrace();
    }

    return childPageIds;
  }

  /**
   * 导出单个页面到Markdown
   */
  public String exportPageToMarkdown(Long knowledgeBaseId, String pageId) {
    String url = confluenceUrl + "/rest/api/content/" + pageId + "?expand=body.storage";
    Request request = new Request.Builder().url(url)
        .addHeader("Authorization", "Bearer " + personalToken)
        .addHeader("Content-Type", "application/json").build();

    try (Response response = client.newCall(request).execute()) {
      if (!response.isSuccessful()) {
        log.warn("❌ 获取页面内容失败 {}: {} {}", pageId, response.code(), response.message());
        return null;
      }

      String json = response.body().string();
      ConfluencePage page = gson.fromJson(json, ConfluencePage.class);

      if (page == null || page.getBody() == null || page.getBody().getStorage() == null) {
        log.warn("❌ 页面内容为空 {}", pageId);
        return null;
      }

      String html = page.getBody().getStorage().getValue();
      String title = page.getTitle();

      log.info("📝 正在处理页面: {} (ID: {})", title, pageId);

      // 设置当前页面ID供draw.io处理使用
      this.currentPageId = pageId;

      if (orderAssistantConfig.getCfBlackListConfig().isIgnore(title)) {
        log.warn("【人工干预】不处理");
        return null;
      }

      String markdown = convertHtmlToMarkdown(html, title);
      if (StringUtils.isEmpty(markdown)) {
        log.warn("【人工干预】不处理");
        return null;
      }

      knowledgeService.insertOrUpdate(String.format("pageId: %s 标题: %s",pageId,title),pageId,markdown,knowledgeBaseId,KnowledgeBaseSource.CF);

      if (orderAssistantConfig.getCfPullWriteFile()) {      // 生成安全的文件名
        String fileName = sanitizeFileName(title) + "_" + pageId + ".md";
        String filePath = outputDir + File.separator + fileName;

        // 写入文件（使用UTF-8编码）
        try (OutputStreamWriter writer = new OutputStreamWriter(new FileOutputStream(filePath),
            StandardCharsets.UTF_8)) {

          writer.write(markdown);
          log.info("✅ 导出完成: {}", fileName);

          return fileName;
        }
      }

    } catch (Exception e) {
      log.warn("❌ 导出页面失败 {}: {}", pageId, e.getMessage());
      e.printStackTrace();
    }

    return null;
  }

  /**
   * 将HTML转换为Markdown
   */
  public String convertHtmlToMarkdown(String html, String title) {
    if (html == null || html.trim().isEmpty()) {
//      return "# " + title + "\n\n*此页面内容为空*\n";
      return TITLE_EMPTY;
    }

    Document doc = Jsoup.parse(html);
    StringBuilder markdown = new StringBuilder();

    // 添加页面标题
    markdown.append("# ").append(title).append("\n\n");

    // 转换body内容
    StringBuilder bodyContent = new StringBuilder();
    if (doc.body() != null) {
      for (Element element : doc.body().children()) {
        String converted = convertElementToMarkdown(element, 0);
        if (!converted.trim().isEmpty()) {
          bodyContent.append(converted).append("\n\n");
//          markdown.append(converted).append("\n\n");
        }
      }
    }
    if (StringUtils.isEmpty(bodyContent.toString())) {
      return BODY_EMPTY;
    }
    markdown.append(bodyContent);
    String result = markdown.toString().trim();

    // 处理draw.io图表标识符
    if(orderAssistantConfig.getCfPullResource()){
      result = processDrawioMacros(result, title);
    }else {
      // 如果不下载图片，就把draw.io的标识删除
      result = removeDrawioMacros(result);
    }

    // 清理markdown格式：移除空行和空标题
    result = cleanupMarkdown(result);

    return result;
  }

  /**
   * 递归转换HTML元素到Markdown
   */
  private String convertElementToMarkdown(Element element, int depth) {
    if (element == null) {
      return "";
    }

    String tagName = element.tagName().toLowerCase();
    StringBuilder result = new StringBuilder();

    switch (tagName) {
      case "h1":
        return "# " + element.text();
      case "h2":
        return "## " + element.text();
      case "h3":
        return "### " + element.text();
      case "h4":
        return "#### " + element.text();
      case "h5":
        return "##### " + element.text();
      case "h6":
        return "###### " + element.text();

      case "p":
        return processInlineElements(element);

      case "br":
        return "\n";

      case "strong":
      case "b":
        return "**" + element.text() + "** ";

      case "em":
      case "i":
        return "*" + element.text() + "* ";

      case "code":
        return "`" + element.text() + "` ";

      case "pre":
        // 检查是否是Confluence代码宏
        if (element.hasClass("code") || element.attr("data-macro-name").equals("code")) {
          return processCodeMacro(element);
        }

        Elements codeElements = element.select("code");
        if (!codeElements.isEmpty()) {
          Element codeElement = codeElements.first();
          String language = codeElement.attr("class").replaceAll(".*language-([^\\s]+).*", "$1");
          if (language.equals(codeElement.attr("class"))) {
            language = ""; // 没有找到语言信息
          }
          String code = cleanCodeContent(codeElement.text());
          return "```" + language + "\n" + code + "\n```";
        } else {
          String code = cleanCodeContent(element.text());
          return "```\n" + code + "\n```";
        }

      case "blockquote":
        String[] lines = element.text().split("\n");
        StringBuilder quote = new StringBuilder();
        for (String line : lines) {
          quote.append("> ").append(line).append("\n");
        }
        return quote.toString().trim();

      case "ul":
        return convertList(element, false, depth);

      case "ol":
        return convertList(element, true, depth);

      case "li":
        // 这个会在convertList中处理
        return processInlineElements(element);

      case "a":
//        return element.text(); // 直接处理为文本，不转换为链接

        String href = element.attr("href");
        String text = element.text();
        if (href.isEmpty()) {
          return text;
        }

        if(orderAssistantConfig.getCfPullResource()){
          // 检查是否是附件链接
          if (isAttachmentLink(href)) {
            String localAttachmentPath = downloadResource(href, "attachment");
            if (localAttachmentPath != null) {
              return "[" + text + "](" + localAttachmentPath + ")";
            }
          }
        }


        // 处理相对链接
        if (href.startsWith("/")) {
          href = confluenceUrl + href;
        }
        return "[" + text + "](" + href + ")";

      case "img":
        return ""; // 不展示图片
//        String src = element.attr("src");
//        String alt = element.attr("alt");
//
//        if(orderAssistantConfig.getCfPullResource()){
//          if (!src.isEmpty()) {
//            String localImagePath = downloadResource(src, "image");
//            if (localImagePath != null) {
//              return "![" + alt + "](" + localImagePath + ")";
//            }
//          }
//        }
//
//        // 如果下载失败，使用原始链接
//        if (src.startsWith("/")) {
//          src = confluenceUrl + src;
//        }
//        return "![" + alt + "](" + src + ")";

      case "table":
        return convertTable(element);

      case "hr":
        return "---";

      default:
        // 检查是否是Confluence宏
        String macroResult = processMacroElement(element);
        if (macroResult != null) {
          return macroResult;
        }

        // 对于未知标签，处理其子元素
        return processInlineElements(element);
    }
  }

  /**
   * 处理Confluence宏元素
   */
  private String processMacroElement(Element element) {
    String className = element.className();
    String tagName = element.tagName().toLowerCase();

    // 通过data-macro-name属性识别宏
    String macroName = element.attr("data-macro-name");
    if (!macroName.isEmpty()) {
      return processMacroByName(element, macroName);
    }

    // 通过class名称识别宏
    if (className.contains("confluence-information-macro")) {
      return processInformationMacro(element);
    } else if (className.contains("expand-container")) {
      return processExpandMacro(element);
    } else if (className.contains("code")) {
      return processCodeMacro(element);
    } else if (className.contains("panel")) {
      return processPanelMacro(element);
    } else if (className.contains("status-macro")) {
      return processStatusMacro(element);
    } else if (className.contains("toc-macro")) {
      return processTocMacro(element);
    } else if (className.contains("excerpt-macro")) {
      return processExcerptMacro(element);
    } else if (className.contains("quote-macro")) {
      return processQuoteMacro(element);
    } else if (className.contains("anchor-macro")) {
      return processAnchorMacro(element);
    } else if (className.contains("include-macro")) {
      return processIncludeMacro(element);
    } else if (className.contains("recently-updated-macro")) {
      return processRecentlyUpdatedMacro(element);
    } else if (className.contains("page-properties-macro")) {
      return processPagePropertiesMacro(element);
    }

    // 通过特定的HTML结构识别
    if ("div".equals(tagName) && element.hasClass("confluence-information-macro-information")) {
      return processInfoBox(element, "info");
    } else if ("div".equals(tagName) && element.hasClass("confluence-information-macro-warning")) {
      return processInfoBox(element, "warning");
    } else if ("div".equals(tagName) && element.hasClass("confluence-information-macro-note")) {
      return processInfoBox(element, "note");
    } else if ("div".equals(tagName) && element.hasClass("confluence-information-macro-tip")) {
      return processInfoBox(element, "tip");
    } else if ("blockquote".equals(tagName)) {
      return processQuoteMacro(element);
    } else if ("a".equals(tagName) && element.hasAttr("name")) {
      return processAnchorMacro(element);
    }

    return null; // 不是已知的宏
  }

  /**
   * 处理内联元素
   */
  private String processInlineElements(Element element) {
    StringBuilder result = new StringBuilder();

    for (Node node : element.childNodes()) {
      if (node instanceof TextNode) {
        result.append(((TextNode) node).text());
      } else if (node instanceof Element) {
        Element childElement = (Element) node;
        result.append(convertElementToMarkdown(childElement, 0));
      }
    }

    return result.toString();
  }

  /**
   * 根据宏名称处理宏
   */
  private String processMacroByName(Element element, String macroName) {
    switch (macroName.toLowerCase()) {
      case "info":
        return processInfoBox(element, "info");
      case "warning":
        return processInfoBox(element, "warning");
      case "note":
        return processInfoBox(element, "note");
      case "tip":
        return processInfoBox(element, "tip");
      case "expand":
        return processExpandMacro(element);
      case "code":
        return processCodeMacro(element);
      case "panel":
        return processPanelMacro(element);
      case "status":
        return processStatusMacro(element);
      case "toc":
        return processTocMacro(element);
      case "excerpt":
        return processExcerptMacro(element);
      case "children":
        return processChildrenMacro(element);
      case "attachments":
        return processAttachmentsMacro(element);
      case "quote":
        return processQuoteMacro(element);
      case "anchor":
        return processAnchorMacro(element);
      case "include":
        return processIncludeMacro(element);
      case "recently-updated":
        return processRecentlyUpdatedMacro(element);
      case "page-properties":
        return processPagePropertiesMacro(element);
      default:
        log.info("🔧 未处理的宏: {}", macroName);
        return processInlineElements(element);
    }
  }

  /**
   * 处理信息框宏（info, warning, note, tip）
   */
  private String processInfoBox(Element element, String type) {
    String content = processInlineElements(element).trim();
    if (content.isEmpty()) {
      return "";
    }

    String icon;
    switch (type.toLowerCase()) {
      case "info":
        icon = "ℹ️";
        break;
      case "warning":
        icon = "⚠️";
        break;
      case "note":
        icon = "📝";
        break;
      case "tip":
        icon = "💡";
        break;
      default:
        icon = "📋";
    }

    // 将内容转换为引用格式
    String[] lines = content.split("\n");
    StringBuilder result = new StringBuilder();
    result.append(icon).append(" **").append(type.toUpperCase()).append("**\n\n");

    for (String line : lines) {
      if (!line.trim().isEmpty()) {
        result.append("> ").append(line).append("\n");
      } else {
        result.append(">\n");
      }
    }

    return result.toString();
  }

  /**
   * 处理信息宏（通用）
   */
  private String processInformationMacro(Element element) {
    // 尝试从class中识别具体类型
    String className = element.className();
    if (className.contains("information")) {
      return processInfoBox(element, "info");
    } else if (className.contains("warning")) {
      return processInfoBox(element, "warning");
    } else if (className.contains("note")) {
      return processInfoBox(element, "note");
    } else if (className.contains("tip")) {
      return processInfoBox(element, "tip");
    }

    return processInfoBox(element, "info"); // 默认为info类型
  }

  /**
   * 处理展开宏
   */
  private String processExpandMacro(Element element) {
    // 查找标题
    Element titleElement = element.selectFirst(".expand-control, .expand-title");
    String title = titleElement != null ? titleElement.text() : "详细信息";

    // 查找内容
    Element contentElement = element.selectFirst(".expand-content");
    String content = contentElement != null ? processInlineElements(contentElement) : processInlineElements(element);

    StringBuilder result = new StringBuilder();
    result.append("📂 **").append(title).append("**\n\n");
    result.append("<details>\n");
    result.append("<summary>点击展开</summary>\n\n");
    result.append(content.trim()).append("\n\n");
    result.append("</details>\n");

    return result.toString();
  }

  /**
   * 处理代码宏
   */
  private String processCodeMacro(Element element) {
    // 尝试获取语言信息
    String language = element.attr("data-language");
    if (language.isEmpty()) {
      language = element.attr("data-syntaxhighlighter-params");
      if (!language.isEmpty() && language.contains("brush:")) {
        language = language.replaceAll(".*brush:\\s*([^;\\s]+).*", "$1");
      }
    }
    if (language.isEmpty()) {
      language = element.attr("class").replaceAll(".*language-([^\\s]+).*", "$1");
    }
    if (language.isEmpty() || language.equals(element.attr("class"))) {
      language = ""; // 没有找到语言信息
    }

    // 处理CDATA内容
    String code = element.text();

    // 如果是Confluence的代码宏，可能包含CDATA
    Elements cdataElements = element.select("![CDATA[*]]");
    if (!cdataElements.isEmpty()) {
      StringBuilder codeBuilder = new StringBuilder();
      for (Element cdataElement : cdataElements) {
        codeBuilder.append(cdataElement.text()).append("\n");
      }
      if (codeBuilder.length() > 0) {
        code = codeBuilder.toString().trim();
      }
    }

    // 如果还是没有内容，尝试获取innerHTML
    if (code.trim().isEmpty()) {
      code = element.html();
      // 清理HTML标签
      code = code.replaceAll("<[^>]+>", "");
      // 解码HTML实体
      code = Jsoup.parse(code).text();
    }

    if (code.trim().isEmpty()) {
      return "";
    }

    // 清理代码内容
    code = cleanCodeContent(code);

    return "```" + language + "\n" + code + "\n```";
  }

  /**
   * 清理代码内容
   */
  private String cleanCodeContent(String code) {
    if (code == null) {
      return "";
    }

    // 移除多余的空行
    code = code.replaceAll("\n{3,}", "\n\n");

    // 移除开头和结尾的空行
    code = code.trim();

    return code;
  }

  /**
   * 转换列表
   */
  private String convertList(Element listElement, boolean ordered, int depth) {
    StringBuilder result = new StringBuilder();
    String indent = repeatString("  ", depth);

    Elements listItems = listElement.children().select("li");
    int counter = 1;

    for (Element li : listItems) {
      if (ordered) {
        result.append(indent).append(counter++).append(". ");
      } else {
        result.append(indent).append("- ");
      }

      // 处理嵌套列表
      Elements nestedLists = li.children().select("ul, ol");
      if (!nestedLists.isEmpty()) {
        // 先处理文本内容
        Element liCopy = li.clone();
        liCopy.select("ul, ol").remove();
        String text = processInlineElements(liCopy).trim();
        if (!text.isEmpty()) {
          result.append(text).append("\n");
        }

        // 然后处理嵌套列表
        for (Element nestedList : nestedLists) {
          boolean isOrdered = "ol".equals(nestedList.tagName());
          result.append(convertList(nestedList, isOrdered, depth + 1));
        }
      } else {
        result.append(processInlineElements(li).trim()).append("\n");
      }
    }

    return result.toString();
  }

  /**
   * 处理面板宏
   */
  private String processPanelMacro(Element element) {
    // 查找面板标题
    Element titleElement = element.selectFirst(".panel-title, .panelHeader");
    String title = titleElement != null ? titleElement.text() : "";

    // 查找面板内容
    Element contentElement = element.selectFirst(".panel-content, .panelContent");
    String content = contentElement != null ? processInlineElements(contentElement) : processInlineElements(element);

    StringBuilder result = new StringBuilder();
    result.append("📋 ");
    if (!title.isEmpty()) {
      result.append("**").append(title).append("**\n\n");
    }

    // 将内容转换为引用格式
    String[] lines = content.split("\n");
    for (String line : lines) {
      if (!line.trim().isEmpty()) {
        result.append("| ").append(line).append("\n");
      } else {
        result.append("|\n");
      }
    }

    return result.toString();
  }

  /**
   * 处理状态宏
   */
  private String processStatusMacro(Element element) {
    String status = element.text().trim();
    String color = element.attr("data-color");

    if (status.isEmpty()) {
      return "";
    }

    // 根据颜色或状态文本选择合适的emoji
    String emoji = "🏷️";
    if (color.contains("green") || status.toLowerCase().contains("done") || status.toLowerCase().contains("完成")) {
      emoji = "✅";
    } else if (color.contains("red") || status.toLowerCase().contains("error") || status.toLowerCase().contains("错误")) {
      emoji = "❌";
    } else if (color.contains("yellow") || status.toLowerCase().contains("warning") || status.toLowerCase().contains("警告")) {
      emoji = "⚠️";
    } else if (color.contains("blue") || status.toLowerCase().contains("info") || status.toLowerCase().contains("信息")) {
      emoji = "ℹ️";
    }

    return emoji + " **" + status + "**";
  }

  /**
   * 处理目录宏
   */
  private String processTocMacro(Element element) {
    return "📑 **目录**\n\n*（目录将根据页面标题自动生成）*\n";
  }

  /**
   * 处理摘要宏
   */
  private String processExcerptMacro(Element element) {
    String content = processInlineElements(element).trim();
    if (content.isEmpty()) {
      return "";
    }

    StringBuilder result = new StringBuilder();
    result.append("📄 **摘要**\n\n");

    // 将摘要内容转换为引用格式
    String[] lines = content.split("\n");
    for (String line : lines) {
      if (!line.trim().isEmpty()) {
        result.append("> ").append(line).append("\n");
      } else {
        result.append(">\n");
      }
    }

    return result.toString();
  }

  /**
   * 处理子页面宏
   */
  private String processChildrenMacro(Element element) {
    return "📁 **子页面**\n\n*（子页面列表将在导出时自动生成）*\n";
  }

  /**
   * 处理附件宏
   */
  private String processAttachmentsMacro(Element element) {
    return "📎 **附件**\n\n*（附件列表将在导出时处理）*\n";
  }

  /**
   * 处理引用宏
   */
  private String processQuoteMacro(Element element) {
    String content = processInlineElements(element).trim();
    if (content.isEmpty()) {
      return "";
    }

    StringBuilder result = new StringBuilder();
    String[] lines = content.split("\n");
    for (String line : lines) {
      if (!line.trim().isEmpty()) {
        result.append("> ").append(line).append("\n");
      } else {
        result.append(">\n");
      }
    }

    return result.toString();
  }

  /**
   * 处理锚点宏
   */
  private String processAnchorMacro(Element element) {
    String anchorName = element.attr("data-anchor-name");
    if (anchorName.isEmpty()) {
      anchorName = element.text().trim();
    }
    if (anchorName.isEmpty()) {
      return "";
    }

    return "<a name=\"" + anchorName + "\"></a>";
  }

  /**
   * 处理包含宏
   */
  private String processIncludeMacro(Element element) {
    String pageTitle = element.attr("data-page-title");
    if (pageTitle.isEmpty()) {
      pageTitle = "其他页面";
    }

    return "📄 **包含内容来自: " + pageTitle + "**\n\n*（包含的页面内容将在导出时处理）*\n";
  }

  /**
   * 处理最近更新宏
   */
  private String processRecentlyUpdatedMacro(Element element) {
    return "🕒 **最近更新**\n\n*（最近更新的页面列表将在导出时生成）*\n";
  }

  /**
   * 处理页面属性宏
   */
  private String processPagePropertiesMacro(Element element) {
    String content = processInlineElements(element).trim();
    if (content.isEmpty()) {
      return "📋 **页面属性**\n\n*（页面属性信息）*\n";
    }

    return "📋 **页面属性**\n\n" + content + "\n";
  }

  /**
   * 转换表格
   */
  private String convertTable(Element table) {
    StringBuilder result = new StringBuilder();
    Elements rows = table.select("tr");

    if (rows.isEmpty()) {
      return "";
    }

    boolean hasHeader = false;

    for (int i = 0; i < rows.size(); i++) {
      Element row = rows.get(i);
      Elements cells = row.select("th, td");

      if (cells.isEmpty()) {
        continue;
      }

      // 检查是否是表头
      if (i == 0 && !row.select("th").isEmpty()) {
        hasHeader = true;
      }

      result.append("|");
      for (Element cell : cells) {
        String cellText = processInlineElements(cell).trim().replace("\n", " ");
        result.append(" ").append(cellText).append(" |");
      }
      result.append("\n");

      // 添加表头分隔符
      if (hasHeader && i == 0) {
        result.append("|");
        for (int j = 0; j < cells.size(); j++) {
          result.append(" --- |");
        }
        result.append("\n");
      }
    }

    return result.toString();
  }

  /**
   * 删除draw.io图表宏标识符
   */
  private String removeDrawioMacros(String content) {
    // 匹配draw.io宏的模式：true[图表名]false[其他参数]true[数字ID]
    // 实际模式类似：truejmanus-statusfalseautotoptrue7811
    Pattern drawioPattern = Pattern.compile("true(.*?)false.*?true(\\d+)");
    Matcher matcher = drawioPattern.matcher(content);

    StringBuffer result = new StringBuffer();
    int matchCount = 0;

    while (matcher.find()) {
      matchCount++;
      String diagramName = matcher.group(1);
      String diagramId = matcher.group(2);

      log.info("🗑️ 删除draw.io图表标识符: {} (ID: {})", diagramName, diagramId);

      // 直接删除，替换为空字符串
      matcher.appendReplacement(result, "");
    }
    matcher.appendTail(result);

    if (matchCount > 0) {
      log.info("🧹 总共删除了 {} 个draw.io图表标识符", matchCount);
    }

    return result.toString();
  }

  /**
   * 处理draw.io图表宏标识符
   */
  private String processDrawioMacros(String content, String pageTitle) {
    // 匹配draw.io宏的模式：true[图表名]false[其他参数]true[数字ID]
    // 实际模式类似：truejmanus-statusfalseautotoptrue7811
    // 匹配draw.io宏的模式：true[图表名]false[参数]true[数字ID]
    Pattern drawioPattern = Pattern.compile("true(.*?)false.*?true(\\d+)");
    Matcher matcher = drawioPattern.matcher(content);

    StringBuffer result = new StringBuffer();
    int matchCount = 0;

    while (matcher.find()) {
      matchCount++;
      String diagramName = matcher.group(1);
      String diagramId = matcher.group(2);

      log.info("🎨 发现draw.io图表: {} (ID: {})", diagramName, diagramId);

      // 尝试下载draw.io图表
      String localImagePath = downloadDrawioDiagram(diagramName, diagramId, pageTitle);
      String replacement;
      if (localImagePath != null) {
        replacement = "\n\n![" + diagramName + "](" + localImagePath + ")\n\n";
      } else {
        replacement = "\n\n*[Draw.io图表: " + diagramName + " - ID: " + diagramId + "]*\n\n";
      }

      matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
    }
    matcher.appendTail(result);

    if (matchCount > 0) {
      log.info("📊 总共处理了 {} 个draw.io图表", matchCount);
    } else {
      log.info("❌ 没有找到draw.io图表模式");
    }

    return result.toString();
  }

  /**
   * 下载draw.io图表
   */
  private String downloadDrawioDiagram(String diagramName, String diagramId, String pageTitle) {
    try {
      log.info("📥 正在尝试下载draw.io图表: {} (ID: {})", diagramName, diagramId);

      String pageId = getCurrentPageId();
      if (pageId == null) {
        log.warn("❌ 无法获取页面ID");
        return null;
      }

      // 尝试多种可能的draw.io导出URL格式
      String[] urlPatterns = {
          // draw.io插件的标准导出API
          "/rest/drawio/1.0/diagram/crud/IMAGE/" + pageId + "/" + diagramId,
          "/rest/drawio/1.0/diagram/crud/PNG/" + pageId + "/" + diagramId,
          "/rest/drawio/1.0/diagram/crud/SVG/" + pageId + "/" + diagramId,

          // 作为附件的可能路径
          "/download/attachments/" + pageId + "/" + sanitizeFileName(diagramName) + ".png",
          "/download/attachments/" + pageId + "/" + sanitizeFileName(diagramName) + ".svg",
          "/download/attachments/" + pageId + "/" + sanitizeFileName(diagramName) + ".drawio.png",

          // 通过附件API查找
          "/rest/api/content/" + pageId + "/child/attachment/" + diagramId,};

      for (String urlPattern : urlPatterns) {
        String fullUrl = confluenceUrl + urlPattern;
        log.info("🔍 尝试URL: {}", urlPattern);

        String localPath = downloadResource(fullUrl, "drawio");
        if (localPath != null) {
          log.info("✅ 成功下载draw.io图表: {}", diagramName);
          return localPath;
        }
      }

      // 如果直接下载失败，尝试通过页面附件列表查找
      String attachmentPath = findDrawioInAttachments(pageId, diagramName, diagramId);
      if (attachmentPath != null) {
        return attachmentPath;
      }

      log.warn("❌ 无法下载draw.io图表: {}", diagramName);
      return null;

    } catch (Exception e) {
      log.warn("❌ 下载draw.io图表异常: {} - {}", diagramName, e.getMessage());
      return null;
    }
  }

  /**
   * 在页面附件中查找draw.io图表
   */
  private String findDrawioInAttachments(String pageId, String diagramName, String diagramId) {
    try {
      String attachmentsUrl = confluenceUrl + "/rest/api/content/" + pageId + "/child/attachment";

      Request request = new Request.Builder().url(attachmentsUrl)
          .addHeader("Authorization", "Bearer " + personalToken)
          .addHeader("Content-Type", "application/json").build();

      try (Response response = client.newCall(request).execute()) {
        if (!response.isSuccessful()) {
          return null;
        }

        String json = response.body().string();
        // 这里可以解析附件列表，查找相关的draw.io文件
        // 由于结构复杂，暂时返回null，后续可以完善
        log.info("📋 获取到附件列表，但暂未实现解析逻辑");

        return null;
      }
    } catch (Exception e) {
      log.warn("❌ 查找附件异常: {}", e.getMessage());
      return null;
    }
  }

  // 临时存储当前处理的页面ID
  private String currentPageId;

  private String getCurrentPageId() {
    return currentPageId;
  }

  /**
   * 判断是否是附件链接
   */
  private boolean isAttachmentLink(String href) {
    if (href == null) {
      return false;
    }

    // Confluence附件链接通常包含这些路径
    return href.contains("/download/attachments/") || href.contains("/download/thumbnails/")
        || href.contains("/download/") ||
        // 检查常见的文件扩展名
        href.matches(
            ".*\\.(pdf|doc|docx|xls|xlsx|ppt|pptx|zip|rar|7z|tar|gz|txt|csv|json|xml)($|\\?.*)");
  }

  /**
   * 下载资源文件
   */
  private String downloadResource(String resourceUrl, String resourceType) {
    try {
      // 处理相对路径
      if (resourceUrl.startsWith("/")) {
        resourceUrl = confluenceUrl + resourceUrl;
      }

      // 如果不是完整URL，跳过
      if (!resourceUrl.startsWith("http")) {
        return null;
      }

      log.info("📥 正在下载资源: " + resourceUrl);

      Request request = new Request.Builder().url(resourceUrl)
          .addHeader("Authorization", "Bearer " + personalToken).build();

      try (Response response = client.newCall(request).execute()) {
        if (!response.isSuccessful()) {
          log.warn("❌ 下载资源失败: " + resourceUrl + " - " + response.code());
          return null;
        }

        // 获取文件扩展名
        String fileName = extractFileNameFromUrl(resourceUrl);
        if (fileName == null) {
          // 根据Content-Type生成文件名
          String contentType = response.header("Content-Type");
          fileName = generateFileName(resourceType, contentType);
        }

        // 确保文件名唯一
        fileName = ensureUniqueFileName(fileName);

        String localPath = resourcesDir + File.separator + fileName;

        // 下载文件
        try (FileOutputStream fos = new FileOutputStream(
            localPath); InputStream is = response.body().byteStream()) {

          byte[] buffer = new byte[8192];
          int bytesRead;
          while ((bytesRead = is.read(buffer)) != -1) {
            fos.write(buffer, 0, bytesRead);
          }
        }

        log.info("✅ 资源下载完成: " + fileName);

        // 返回相对路径
        return "./resources/" + fileName;

      }
    } catch (Exception e) {
      log.warn("❌ 下载资源异常: " + resourceUrl + " - " + e.getMessage());
      return null;
    }
  }

  /**
   * 从URL中提取文件名
   */
  private String extractFileNameFromUrl(String url) {
    try {
      // 移除查询参数
      int queryIndex = url.indexOf('?');
      if (queryIndex != -1) {
        url = url.substring(0, queryIndex);
      }

      // 获取最后一个/后的内容
      int lastSlash = url.lastIndexOf('/');
      if (lastSlash != -1 && lastSlash < url.length() - 1) {
        String fileName = url.substring(lastSlash + 1);
        // 检查是否有有效的文件扩展名
        if (fileName.contains(".") && fileName.lastIndexOf('.') < fileName.length() - 1) {
          return sanitizeFileName(fileName);
        }
      }
    } catch (Exception e) {
      // 忽略异常，返回null
    }
    return null;
  }

  /**
   * 根据资源类型和Content-Type生成文件名
   */
  private String generateFileName(String resourceType, String contentType) {
    String extension = ".bin"; // 默认扩展名

    if (contentType != null) {
      contentType = contentType.toLowerCase();
      if (contentType.contains("image/jpeg") || contentType.contains("image/jpg")) {
        extension = ".jpg";
      } else if (contentType.contains("image/png")) {
        extension = ".png";
      } else if (contentType.contains("image/gif")) {
        extension = ".gif";
      } else if (contentType.contains("image/svg")) {
        extension = ".svg";
      } else if (contentType.contains("image/webp")) {
        extension = ".webp";
      } else if (contentType.contains("application/pdf")) {
        extension = ".pdf";
      } else if (contentType.contains("text/plain")) {
        extension = ".txt";
      } else if (contentType.contains("application/json")) {
        extension = ".json";
      }
    }

    return resourceType + "_" + System.currentTimeMillis() + extension;
  }

  /**
   * 确保文件名唯一
   */
  private String ensureUniqueFileName(String fileName) {
    File file = new File(resourcesDir, fileName);
    if (!file.exists()) {
      return fileName;
    }

    // 如果文件已存在，添加数字后缀
    String nameWithoutExt = fileName;
    String extension = "";

    int lastDot = fileName.lastIndexOf('.');
    if (lastDot != -1) {
      nameWithoutExt = fileName.substring(0, lastDot);
      extension = fileName.substring(lastDot);
    }

    int counter = 1;
    while (file.exists()) {
      fileName = nameWithoutExt + "_" + counter + extension;
      file = new File(resourcesDir, fileName);
      counter++;
    }

    return fileName;
  }

  /**
   * 重复字符串（Java 8兼容）
   */
  private String repeatString(String str, int count) {
    if (count <= 0) {
      return "";
    }
    StringBuilder sb = new StringBuilder();
    for (int i = 0; i < count; i++) {
      sb.append(str);
    }
    return sb.toString();
  }

  /**
   * 生成安全的文件名
   */
  private String sanitizeFileName(String fileName) {
    if (fileName == null || fileName.trim().isEmpty()) {
      return "untitled";
    }

    // 移除或替换不安全的字符
    return fileName.trim().replaceAll("[\\\\/:*?\"<>|]", "_").replaceAll("\\s+", "_")
        .replaceAll("_{2,}", "_").replaceAll("^_|_$", "");
  }

  /**
   * 清理markdown格式：移除连续空行和空标题
   */
  private String cleanupMarkdown(String markdown) {
    if (markdown == null || markdown.trim().isEmpty()) {
      return markdown;
    }

    // 1. 先处理连续空行，将多个连续空行合并为单个空行
    String[] lines = markdown.split("\n", -1); // -1保留尾部空字符串
    List<String> processedLines = new ArrayList<>();

    boolean lastLineWasEmpty = false;
    for (String line : lines) {
      boolean currentLineIsEmpty = line.trim().isEmpty();

      if (currentLineIsEmpty) {
        // 如果当前行是空行，且上一行不是空行，则保留这个空行
        if (!lastLineWasEmpty) {
          processedLines.add("");
        }
        // 如果上一行也是空行，则跳过当前空行（不添加）
      } else {
        // 非空行直接添加
        processedLines.add(line);
      }

      lastLineWasEmpty = currentLineIsEmpty;
    }

    // 2. 移除空标题（标题下面没有内容的）
    List<String> finalLines = new ArrayList<>();

    for (int i = 0; i < processedLines.size(); i++) {
      String currentLine = processedLines.get(i);

      // 检查当前行是否是标题
      if (isHeading(currentLine)) {
        // 查找下一个标题之前是否有实际内容
        boolean hasContent = false;

        for (int j = i + 1; j < processedLines.size(); j++) {
          String nextLine = processedLines.get(j);

          // 如果遇到下一个标题，停止查找
          if (isHeading(nextLine)) {
            break;
          }

          // 如果找到非空的内容行，说明当前标题有内容
          if (!nextLine.trim().isEmpty()) {
            hasContent = true;
            break;
          }
        }

        // 只有当标题下有内容时才保留
        if (hasContent) {
          finalLines.add(currentLine);
        }
      } else {
        // 非标题行直接保留（包括空行）
        finalLines.add(currentLine);
      }
    }

    // 3. 重新组合，保持原有的换行结构
    StringBuilder result = new StringBuilder();
    for (int i = 0; i < finalLines.size(); i++) {
      result.append(finalLines.get(i));
      if (i < finalLines.size() - 1) {
        result.append("\n");
      }
    }

    return result.toString();
  }

  /**
   * 判断一行是否是标题
   */
  private boolean isHeading(String line) {
    if (line == null || line.trim().isEmpty()) {
      return false;
    }

    String trimmed = line.trim();
    // 检查是否以#开头的markdown标题
    return trimmed.matches("^#{1,6}\\s+.*");
  }

  /**
   * 生成索引文件
   */
  private void generateIndexFile(List<String> exportedFiles) {
    if (exportedFiles.isEmpty()) {
      return;
    }

    try {
      String indexPath = outputDir + File.separator + "README.md";
      try (OutputStreamWriter writer = new OutputStreamWriter(new FileOutputStream(indexPath),
          StandardCharsets.UTF_8)) {
        writer.write("# Confluence 导出索引\n\n");
        writer.write("导出时间: " + new Date() + "\n\n");
        writer.write("## 导出的页面\n\n");

        for (String fileName : exportedFiles) {
          String displayName = fileName.replace(".md", "").replaceAll("_\\d+$", "");
          writer.write("- [" + displayName + "](./" + fileName + ")\n");
        }

        log.info("📋 生成索引文件: README.md");
      }
    } catch (IOException e) {
      log.warn("❌ 生成索引文件失败: " + e.getMessage());
    }
  }

  /**
   * 测试宏处理功能的方法
   */
  public void testMacroProcessing() {
    log.info("🧪 开始测试Confluence宏处理功能");

    // 测试信息框宏
    String infoHtml = "<div class=\"confluence-information-macro confluence-information-macro-information\">" +
        "<div class=\"confluence-information-macro-body\">这是一个信息提示</div></div>";
    String infoResult = convertHtmlToMarkdown(infoHtml, "信息框测试");
    log.info("📋 信息框宏处理结果:\n{}", infoResult);

    // 测试代码宏
    String codeHtml = "<div data-macro-name=\"code\" data-language=\"java\">" +
        "<pre><code>public class Test {\n    public static void main(String[] args) {\n        System.out.println(\"Hello World\");\n    }\n}</code></pre></div>";
    String codeResult = convertHtmlToMarkdown(codeHtml, "代码宏测试");
    log.info("💻 代码宏处理结果:\n{}", codeResult);

    // 测试展开宏
    String expandHtml = "<div class=\"expand-container\">" +
        "<div class=\"expand-title\">点击查看详情</div>" +
        "<div class=\"expand-content\">这里是展开的内容</div></div>";
    String expandResult = convertHtmlToMarkdown(expandHtml, "展开宏测试");
    log.info("📂 展开宏处理结果:\n{}", expandResult);

    // 测试状态宏
    String statusHtml = "<span class=\"status-macro\" data-color=\"green\">已完成</span>";
    String statusResult = convertHtmlToMarkdown(statusHtml, "状态宏测试");
    log.info("🏷️ 状态宏处理结果:\n{}", statusResult);

    log.info("✅ 宏处理功能测试完成");
  }
}
