package com.yxt.order.assistant.prompt.component;

import com.yxt.order.assistant.prompt.core.PromptComponentType;
import com.yxt.order.assistant.prompt.core.PromptContext;

/**
 * 示例组件
 * 用于提供输入输出示例，支持少样本学习
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public class ExampleComponent extends AbstractPromptComponent {
    
    private final String input;
    private final String output;
    
    public ExampleComponent(String input, String output) {
        super(PromptComponentType.EXAMPLE, null);
        this.input = input;
        this.output = output;
    }
    
    public ExampleComponent(String input, String output, int priority) {
        super(PromptComponentType.EXAMPLE, null, priority);
        this.input = input;
        this.output = output;
    }
    
    @Override
    public String getContent(PromptContext context) {
        return formatExample(input, output);
    }
    
    /**
     * 格式化示例内容
     * 
     * @param input 输入示例
     * @param output 输出示例
     * @return 格式化后的示例内容
     */
    private String formatExample(String input, String output) {
        StringBuilder sb = new StringBuilder();
        sb.append("示例:\n");
        sb.append("输入: ").append(input != null ? input : "").append("\n");
        sb.append("输出: ").append(output != null ? output : "");
        return sb.toString();
    }
    
    @Override
    public boolean validate(PromptContext context) {
        // 示例组件需要有输入和输出
        return input != null && !input.trim().isEmpty() && 
               output != null && !output.trim().isEmpty();
    }
    
    /**
     * 获取输入示例
     * 
     * @return 输入示例
     */
    public String getInput() {
        return input;
    }
    
    /**
     * 获取输出示例
     * 
     * @return 输出示例
     */
    public String getOutput() {
        return output;
    }
    
    /**
     * 创建示例组件
     * 
     * @param input 输入示例
     * @param output 输出示例
     * @return 示例组件实例
     */
    public static ExampleComponent of(String input, String output) {
        return new ExampleComponent(input, output);
    }
    
    /**
     * 创建带优先级的示例组件
     * 
     * @param input 输入示例
     * @param output 输出示例
     * @param priority 优先级
     * @return 示例组件实例
     */
    public static ExampleComponent of(String input, String output, int priority) {
        return new ExampleComponent(input, output, priority);
    }
}
