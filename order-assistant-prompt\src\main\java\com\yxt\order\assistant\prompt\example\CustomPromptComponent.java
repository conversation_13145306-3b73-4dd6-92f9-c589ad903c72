package com.yxt.order.assistant.prompt.example;

import com.yxt.order.assistant.prompt.core.PromptComponent;
import com.yxt.order.assistant.prompt.core.PromptComponentType;
import com.yxt.order.assistant.prompt.core.PromptContext;

import java.util.Map;

/**
 * 自定义提示词组件示例
 * 展示如何创建自定义组件
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public class CustomPromptComponent implements PromptComponent {
    
    private final String content;
    private final int priority;
    
    public CustomPromptComponent(String content) {
        this(content, PromptComponentType.CUSTOM.getDefaultPriority());
    }
    
    public CustomPromptComponent(String content, int priority) {
        this.content = content;
        this.priority = priority;
    }
    
    @Override
    public PromptComponentType getType() {
        return PromptComponentType.CUSTOM;
    }
    
    @Override
    public String getContent(PromptContext context) {
        if (content == null || content.trim().isEmpty()) {
            return "";
        }
        
        // 自定义内容处理逻辑
        StringBuilder sb = new StringBuilder();
        sb.append("## 自定义组件\n");
        sb.append("处理时间: ").append(java.time.LocalDateTime.now()).append("\n");
        sb.append("内容: ").append(content).append("\n");
        
        // 可以根据上下文动态调整内容
        if (context.hasVariable("customPrefix")) {
            String prefix = context.getVariable("customPrefix", "");
            sb.insert(0, prefix + "\n");
        }
        
        return sb.toString();
    }
    
    @Override
    public int getPriority() {
        return priority;
    }
    
    @Override
    public boolean validate(PromptContext context) {
        // 自定义验证逻辑
        return content != null && !content.trim().isEmpty();
    }
    
    @Override
    public Map<String, Class<?>> getVariableDependencies() {
        // 定义变量依赖
        Map<String, Class<?>> dependencies = new java.util.HashMap<>();
        dependencies.put("customPrefix", String.class);  // 可选依赖
        return dependencies;
    }
    
    @Override
    public boolean isRequired() {
        return false;  // 自定义组件通常不是必需的
    }
}
