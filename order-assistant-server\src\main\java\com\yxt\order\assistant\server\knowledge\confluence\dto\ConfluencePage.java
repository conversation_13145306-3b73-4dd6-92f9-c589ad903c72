package com.yxt.order.assistant.server.knowledge.confluence.dto;

/**
 * Confluence页面信息
 */
public class ConfluencePage {
    private String id;
    private String type;
    private String status;
    private String title;
    private ConfluenceBody body;
    private ConfluenceLinks _links;
    
    public static class ConfluenceBody {
        private ConfluenceStorage storage;
        
        public ConfluenceStorage getStorage() {
            return storage;
        }
        
        public void setStorage(ConfluenceStorage storage) {
            this.storage = storage;
        }
    }
    
    public static class ConfluenceStorage {
        private String value;
        private String representation;
        
        public String getValue() {
            return value;
        }
        
        public void setValue(String value) {
            this.value = value;
        }
        
        public String getRepresentation() {
            return representation;
        }
        
        public void setRepresentation(String representation) {
            this.representation = representation;
        }
    }
    
    public static class ConfluenceLinks {
        private String webui;
        private String tinyui;
        private String self;
        
        public String getWebui() {
            return webui;
        }
        
        public void setWebui(String webui) {
            this.webui = webui;
        }
        
        public String getTinyui() {
            return tinyui;
        }
        
        public void setTinyui(String tinyui) {
            this.tinyui = tinyui;
        }
        
        public String getSelf() {
            return self;
        }
        
        public void setSelf(String self) {
            this.self = self;
        }
    }
    
    // Getters and Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public ConfluenceBody getBody() {
        return body;
    }
    
    public void setBody(ConfluenceBody body) {
        this.body = body;
    }
    
    public ConfluenceLinks get_links() {
        return _links;
    }
    
    public void set_links(ConfluenceLinks _links) {
        this._links = _links;
    }
}
