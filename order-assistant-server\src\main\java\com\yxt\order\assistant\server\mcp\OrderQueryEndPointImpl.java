package com.yxt.order.assistant.server.mcp;

import com.yxt.domain.order.mcp.req.QueryOrderMakeNoReq;
import com.yxt.domain.order.mcp.res.QueryOrderMakeNoRes;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.order.assistant.server.mcp.feign.HydeeBusineessOrderMcpApiFeign;
import com.yxt.order.assistant.server.mcp.feign.OrderMcpApiFeign;
import com.yxt.order.atom.sdk.mcp.req.OrderDetailQueryReq;
import com.yxt.order.atom.sdk.mcp.req.OrderTypeQueryReq;
import com.yxt.order.atom.sdk.mcp.res.OrderTypeQueryRes;
import com.yxt.order.atom.sdk.online_order.order_info.dto.res.FullOrderDtoResDto;
import jakarta.annotation.Resource;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class OrderQueryEndPointImpl implements OrderQueryEndPoint {

  @Resource
  private OrderMcpApiFeign orderMcpApiFeign;

  @Resource
  private HydeeBusineessOrderMcpApiFeign hydeeBusineessOrderMcpApiFeign;

  @Override
  public ResponseBase<OrderTypeQueryRes> queryOrderType(OrderTypeQueryReq orderTypeReq) {
    return orderMcpApiFeign.orderType(orderTypeReq);
  }

  @Override
  public ResponseBase<FullOrderDtoResDto> queryOrderDetail(
      OrderDetailQueryReq orderDetailQueryReq) {
    return orderMcpApiFeign.orderDetail(orderDetailQueryReq);
  }

  @Override
  public ResponseBase<QueryOrderMakeNoRes> queryOrderMakeNo(
      QueryOrderMakeNoReq queryOrderMakeNoReq) {
    return hydeeBusineessOrderMcpApiFeign.queryOrderMakeNo(
        queryOrderMakeNoReq);
  }

}
