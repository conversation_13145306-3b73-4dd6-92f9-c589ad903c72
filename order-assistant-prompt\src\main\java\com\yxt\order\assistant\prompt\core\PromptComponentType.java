package com.yxt.order.assistant.prompt.core;

/**
 * 提示词组件类型枚举
 * 定义了支持的所有提示词组件类型及其默认优先级
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public enum PromptComponentType {
    
    /**
     * 系统提示词 - 定义AI的角色和基本行为
     */
    SYSTEM(10, "System Prompt"),
    
    /**
     * 上下文信息 - 提供背景信息和环境设置
     */
    CONTEXT(20, "Context"),
    
    /**
     * 指令 - 具体的任务指令
     */
    INSTRUCTION(30, "Instruction"),
    
    /**
     * 示例 - 输入输出示例，用于少样本学习
     */
    EXAMPLE(40, "Example"),
    
    /**
     * 约束条件 - 对输出的限制和要求
     */
    CONSTRAINT(50, "Constraint"),
    
    /**
     * 输出格式 - 定义期望的输出格式
     */
    OUTPUT_FORMAT(60, "Output Format"),
    
    /**
     * 用户输入 - 用户的具体输入内容
     */
    USER(70, "User Input"),
    
    /**
     * 助手回复 - AI助手的回复，用于对话上下文
     */
    ASSISTANT(80, "Assistant Reply"),
    
    /**
     * 自定义组件 - 用户自定义的组件类型
     */
    CUSTOM(90, "Custom Component");
    
    private final int defaultPriority;
    private final String displayName;
    
    PromptComponentType(int defaultPriority, String displayName) {
        this.defaultPriority = defaultPriority;
        this.displayName = displayName;
    }
    
    /**
     * 获取默认优先级
     * 
     * @return 默认优先级
     */
    public int getDefaultPriority() {
        return defaultPriority;
    }
    
    /**
     * 获取显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        return displayName;
    }
}
