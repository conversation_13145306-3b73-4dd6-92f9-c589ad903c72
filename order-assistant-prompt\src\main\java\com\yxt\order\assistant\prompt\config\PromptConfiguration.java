package com.yxt.order.assistant.prompt.config;

import com.yxt.order.assistant.prompt.core.PromptTemplate;
import com.yxt.order.assistant.prompt.template.SimplePromptTemplate;
import com.yxt.order.assistant.prompt.template.VelocityPromptTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 提示词框架Spring配置类
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
@Configuration
@EnableConfigurationProperties(PromptProperties.class)
public class PromptConfiguration {
    
    private final PromptProperties properties;
    
    public PromptConfiguration(PromptProperties properties) {
        this.properties = properties;
    }
    
    /**
     * 配置提示词模板引擎
     * 
     * @return 模板引擎实例
     */
    @Bean
    @ConditionalOnMissingBean
    public PromptTemplate promptTemplate() {
        switch (properties.getTemplateEngine()) {
            case VELOCITY:
                return new VelocityPromptTemplate(
                        properties.getTemplatePath(),
                        properties.getDefaultEncoding()
                );
            case SIMPLE:
            default:
                return new SimplePromptTemplate();
        }
    }
}
