package com.yxt.order.assistant.prompt.builder;

import com.yxt.order.assistant.prompt.component.*;
import com.yxt.order.assistant.prompt.context.DefaultPromptContext;
import com.yxt.order.assistant.prompt.core.*;
import com.yxt.order.assistant.prompt.exception.PromptException;
import com.yxt.order.assistant.prompt.template.SimplePromptTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 默认提示词构建器实现
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public class DefaultPromptBuilder implements PromptBuilder {
    
    private final List<PromptComponent> components;
    private final List<PromptProcessor> processors;
    private PromptContext context;
    private PromptTemplate template;
    private String templateName;
    
    public DefaultPromptBuilder() {
        this.components = new ArrayList<>();
        this.processors = new ArrayList<>();
        this.context = new DefaultPromptContext();
        this.template = new SimplePromptTemplate();
    }
    
    @Override
    public PromptBuilder withSystem(String systemPrompt) {
        if (systemPrompt != null && !systemPrompt.trim().isEmpty()) {
            components.add(new SystemPromptComponent(systemPrompt));
        }
        return this;
    }
    
    @Override
    public PromptBuilder withUser(String userInput) {
        if (userInput != null && !userInput.trim().isEmpty()) {
            components.add(new UserPromptComponent(userInput));
        }
        return this;
    }
    
    @Override
    public PromptBuilder withAssistant(String assistantReply) {
        if (assistantReply != null && !assistantReply.trim().isEmpty()) {
            components.add(new AssistantPromptComponent(assistantReply));
        }
        return this;
    }
    
    @Override
    public PromptBuilder withContext(String context) {
        if (context != null && !context.trim().isEmpty()) {
            components.add(new ContextComponent(context));
        }
        return this;
    }
    
    @Override
    public PromptBuilder withContext(String key, Object value) {
        if (key != null && !key.trim().isEmpty() && value != null) {
            components.add(new ContextComponent(key, value));
        }
        return this;
    }
    
    @Override
    public PromptBuilder withInstruction(String instruction) {
        if (instruction != null && !instruction.trim().isEmpty()) {
            components.add(new InstructionComponent(instruction));
        }
        return this;
    }
    
    @Override
    public PromptBuilder withExample(String input, String output) {
        if (input != null && !input.trim().isEmpty() && 
            output != null && !output.trim().isEmpty()) {
            components.add(new ExampleComponent(input, output));
        }
        return this;
    }
    
    @Override
    public PromptBuilder withConstraint(String constraint) {
        if (constraint != null && !constraint.trim().isEmpty()) {
            components.add(new ConstraintComponent(constraint));
        }
        return this;
    }
    
    @Override
    public PromptBuilder withOutputFormat(String outputFormat) {
        if (outputFormat != null && !outputFormat.trim().isEmpty()) {
            components.add(new OutputFormatComponent(outputFormat));
        }
        return this;
    }
    
    @Override
    public PromptBuilder withComponent(PromptComponent component) {
        if (component != null) {
            components.add(component);
        }
        return this;
    }
    
    @Override
    public PromptBuilder withTemplate(String templateName) {
        this.templateName = templateName;
        return this;
    }
    
    @Override
    public PromptBuilder withVariable(String key, Object value) {
        if (key != null && value != null) {
            context.setVariable(key, value);
        }
        return this;
    }
    
    @Override
    public PromptBuilder withVariables(Map<String, Object> variables) {
        if (variables != null) {
            context.setVariables(variables);
        }
        return this;
    }
    
    @Override
    public PromptBuilder withProcessor(PromptProcessor processor) {
        if (processor != null) {
            processors.add(processor);
        }
        return this;
    }
    
    @Override
    public PromptBuilder withContext(PromptContext context) {
        if (context != null) {
            this.context = context;
        }
        return this;
    }
    
    @Override
    public PromptResult build() {
        try {
            // 验证必需组件
            validateRequiredComponents();
            
            // 处理组件
            List<PromptComponent> processedComponents = processComponents();
            
            // 构建内容
            String content = buildContent(processedComponents);
            
            // 应用模板
            if (templateName != null && !templateName.trim().isEmpty()) {
                content = applyTemplate(content);
            }
            
            return PromptResult.builder()
                    .content(content)
                    .components(processedComponents)
                    .context(context)
                    .success(true)
                    .build();
                    
        } catch (Exception e) {
            return PromptResult.builder()
                    .content("")
                    .components(components)
                    .context(context)
                    .success(false)
                    .error(e)
                    .build();
        }
    }
    
    /**
     * 验证必需组件
     */
    private void validateRequiredComponents() {
        List<String> missingComponents = new ArrayList<>();
        
        for (PromptComponent component : components) {
            if (component.isRequired() && !component.validate(context)) {
                missingComponents.add(component.getType().getDisplayName());
            }
        }
        
        if (!missingComponents.isEmpty()) {
            throw new PromptException("Missing required components: " + 
                    String.join(", ", missingComponents));
        }
    }
    
    /**
     * 处理组件
     */
    private List<PromptComponent> processComponents() {
        List<PromptComponent> result = new ArrayList<>(components);
        
        // 按优先级排序处理器
        List<PromptProcessor> sortedProcessors = processors.stream()
                .filter(processor -> processor.isApplicable(context))
                .sorted(Comparator.comparingInt(PromptProcessor::getPriority))
                .collect(Collectors.toList());
        
        // 依次应用处理器
        for (PromptProcessor processor : sortedProcessors) {
            result = processor.process(result, context);
        }
        
        // 按优先级排序组件
        result.sort(Comparator.comparingInt(PromptComponent::getPriority));
        
        return result;
    }
    
    /**
     * 构建内容
     */
    private String buildContent(List<PromptComponent> components) {
        StringBuilder sb = new StringBuilder();
        
        for (PromptComponent component : components) {
            String componentContent = component.getContent(context);
            if (componentContent != null && !componentContent.trim().isEmpty()) {
                if (sb.length() > 0) {
                    sb.append("\n\n");
                }
                sb.append(componentContent);
            }
        }
        
        return sb.toString();
    }
    
    /**
     * 应用模板
     */
    private String applyTemplate(String content) {
        if (template == null) {
            return content;
        }
        
        Map<String, Object> variables = context.getAllVariables();
        variables.put("content", content);
        
        if (template.templateExists(templateName)) {
            return template.renderTemplate(templateName, variables);
        } else {
            // 如果模板文件不存在，将模板名称作为模板内容处理
            return template.render(templateName, variables);
        }
    }
}
