package com.yxt.order.assistant.prompt.processor;

import com.yxt.order.assistant.prompt.component.AbstractPromptComponent;
import com.yxt.order.assistant.prompt.core.*;
import com.yxt.order.assistant.prompt.template.SimplePromptTemplate;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 模板处理器
 * 用于处理组件中的模板变量
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public class TemplateProcessor implements PromptProcessor {
    
    private final PromptTemplate template;
    
    public TemplateProcessor() {
        this(new SimplePromptTemplate());
    }
    
    public TemplateProcessor(PromptTemplate template) {
        this.template = template;
    }
    
    @Override
    public List<PromptComponent> process(List<PromptComponent> components, PromptContext context) {
        if (components == null || components.isEmpty()) {
            return components;
        }
        
        Map<String, Object> variables = context.getAllVariables();
        
        return components.stream()
                .map(component -> processComponent(component, variables))
                .collect(Collectors.toList());
    }
    
    /**
     * 处理单个组件
     * 
     * @param component 原始组件
     * @param variables 变量映射
     * @return 处理后的组件
     */
    private PromptComponent processComponent(PromptComponent component, Map<String, Object> variables) {
        return new TemplateProcessedComponent(component, template, variables);
    }
    
    @Override
    public String getName() {
        return "TemplateProcessor";
    }
    
    @Override
    public int getPriority() {
        return 50; // 中等优先级
    }
    
    /**
     * 模板处理后的组件包装器
     */
    private static class TemplateProcessedComponent implements PromptComponent {
        
        private final PromptComponent originalComponent;
        private final PromptTemplate template;
        private final Map<String, Object> variables;
        
        public TemplateProcessedComponent(PromptComponent originalComponent, 
                                        PromptTemplate template, 
                                        Map<String, Object> variables) {
            this.originalComponent = originalComponent;
            this.template = template;
            this.variables = variables;
        }
        
        @Override
        public PromptComponentType getType() {
            return originalComponent.getType();
        }
        
        @Override
        public String getContent(PromptContext context) {
            String originalContent = originalComponent.getContent(context);
            if (originalContent == null || originalContent.trim().isEmpty()) {
                return originalContent;
            }
            
            try {
                return template.render(originalContent, variables);
            } catch (Exception e) {
                // 如果模板处理失败，返回原始内容
                System.err.println("Template processing failed for component " + 
                        getType().getDisplayName() + ": " + e.getMessage());
                return originalContent;
            }
        }
        
        @Override
        public int getPriority() {
            return originalComponent.getPriority();
        }
        
        @Override
        public boolean validate(PromptContext context) {
            return originalComponent.validate(context);
        }
        
        @Override
        public Map<String, Class<?>> getVariableDependencies() {
            return originalComponent.getVariableDependencies();
        }
        
        @Override
        public boolean isRequired() {
            return originalComponent.isRequired();
        }
    }
}
