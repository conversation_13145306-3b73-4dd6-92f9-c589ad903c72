package com.yxt.order.assistant.server.repository.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * AnswerFailed Entity 对应表：answer_failed
 */

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@TableName("answer_failed")
public class AnswerFailed {

  @TableId(value = "id", type = IdType.AUTO)
  private Long id;

  /**
   * 状态 WAIT - 待处理 HANDLED - 已处理
   */
  private String status;

  /**
   * 用户查询的内容
   */
  private String query;

  /**
   * 知识库ID
   */
  private Integer knowledgeBaseId;

  /**
   * 知识ID
   */
  private Integer knowledgeId;
}
