package com.yxt.order.assistant.server.config;


import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "order.assistant")
public class OrderAssistantConfig {

  private Dify dify;
  private CfBlackListConfig cfBlackListConfig;
  private Boolean cfPullResource = false;  // 默认不下载资源。模型处理成本高。
  private Boolean cfPullWriteFile = false;
  // 合并合并表的数据库
  private List<DbTable> dbTableList;

  private String defaultSeparator = "|||||order|||||";


  public String getDatabaseSelectSeq(String databaseName) {
    Optional<DbTable> first = dbTableList.stream().filter(s -> s.getDataBase().equals(databaseName))
        .findFirst();
    if (first.isPresent()) {
      return first.get().getSelectSeq();
    }
    throw new RuntimeException(String.format("含分表的数据库 %s 未配置选定的分表键", databaseName));
  }

  public Boolean isNeedMergeTable(String databaseName) {
    if (CollectionUtils.isEmpty(dbTableList)) {
      return false;
    }
    Set<String> collect = dbTableList.stream().map(DbTable::getDataBase)
        .collect(Collectors.toSet());
    return collect.contains(databaseName);
  }

  /**
   * Dify配置
   */
  @Data
  public static class Dify {

    private String baseUrl;
    private String apiKey;
    // 知识设置
    private KnowledgeSetting knowledgeSetting;

    @Data
    private static class KnowledgeSetting {
      //分段标识符
      private String delimiter = "||||||||||||";
      //分段最大长度 4000
      private Long maximumChunkLength = 4000L;
      //分段重叠长度
      private Double chunkOverlap = maximumChunkLength * 0.25;
    }
  }

  /**
   * 数据库表配置
   */
  @Data
  public static class DbTable {

    /**
     * 数据库名
     */
    private String dataBase;

    /**
     * 选择一个序列
     */
    private String selectSeq;
  }


  @Slf4j
  @Data
  public static class CfBlackListConfig {

    private List<String> pageIdList;
    private List<String> titleList;
    private List<String> ignoreLike; // 模糊匹配,忽略


    public boolean isBlackList(String pageId, String title) {
      boolean black = pageIdList.contains(pageId) || titleList.contains(title);
      if (black) {
        log.info("pageId:{} title:{} 命中黑名单,不收纳进知识库", pageId, title);
      }
      return black;
    }

    public boolean isIgnore(String title) {
      for (String ignore : ignoreLike) {
        if (title.toLowerCase().contains(ignore.toLowerCase())) {
          log.info("title(ignore):{} 命中忽略规则,不收纳进知识库", title);
          return true;
        }
      }
      return false;
    }


  }


}
