package com.yxt.order.assistant.prompt.component;

import com.yxt.order.assistant.prompt.core.PromptComponent;
import com.yxt.order.assistant.prompt.core.PromptComponentType;
import com.yxt.order.assistant.prompt.core.PromptContext;

import java.util.Map;

/**
 * 提示词组件抽象基类
 * 提供通用的组件实现逻辑
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public abstract class AbstractPromptComponent implements PromptComponent {
    
    protected final PromptComponentType type;
    protected final String content;
    protected final int priority;
    protected final boolean required;
    
    protected AbstractPromptComponent(PromptComponentType type, String content) {
        this(type, content, type.getDefaultPriority(), false);
    }
    
    protected AbstractPromptComponent(PromptComponentType type, String content, int priority) {
        this(type, content, priority, false);
    }
    
    protected AbstractPromptComponent(PromptComponentType type, String content, boolean required) {
        this(type, content, type.getDefaultPriority(), required);
    }
    
    protected AbstractPromptComponent(PromptComponentType type, String content, int priority, boolean required) {
        this.type = type;
        this.content = content;
        this.priority = priority;
        this.required = required;
    }
    
    @Override
    public PromptComponentType getType() {
        return type;
    }
    
    @Override
    public String getContent(PromptContext context) {
        if (content == null || content.trim().isEmpty()) {
            return "";
        }
        
        // 子类可以重写此方法来实现更复杂的内容生成逻辑
        return processContent(content, context);
    }
    
    /**
     * 处理内容，子类可以重写此方法来实现自定义的内容处理逻辑
     * 
     * @param rawContent 原始内容
     * @param context 上下文信息
     * @return 处理后的内容
     */
    protected String processContent(String rawContent, PromptContext context) {
        return rawContent;
    }
    
    @Override
    public int getPriority() {
        return priority;
    }
    
    @Override
    public boolean isRequired() {
        return required;
    }
    
    @Override
    public boolean validate(PromptContext context) {
        // 基础验证：检查必需组件是否有内容
        if (required && (content == null || content.trim().isEmpty())) {
            return false;
        }
        
        // 验证变量依赖
        Map<String, Class<?>> dependencies = getVariableDependencies();
        for (Map.Entry<String, Class<?>> entry : dependencies.entrySet()) {
            String varName = entry.getKey();
            Class<?> varType = entry.getValue();
            
            if (!context.hasVariable(varName)) {
                return false;
            }
            
            if (!context.getVariable(varName, varType).isPresent()) {
                return false;
            }
        }
        
        return true;
    }
    
    @Override
    public Map<String, Class<?>> getVariableDependencies() {
        return java.util.Collections.emptyMap();
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        AbstractPromptComponent that = (AbstractPromptComponent) obj;
        return priority == that.priority &&
                required == that.required &&
                type == that.type &&
                java.util.Objects.equals(content, that.content);
    }
    
    @Override
    public int hashCode() {
        return java.util.Objects.hash(type, content, priority, required);
    }
    
    @Override
    public String toString() {
        return getClass().getSimpleName() + "{" +
                "type=" + type +
                ", priority=" + priority +
                ", required=" + required +
                ", contentLength=" + (content != null ? content.length() : 0) +
                '}';
    }
}
