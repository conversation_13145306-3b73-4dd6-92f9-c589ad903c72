package com.yxt.order.assistant.prompt.component;

import com.yxt.order.assistant.prompt.core.PromptComponentType;

/**
 * 输出格式组件
 * 用于定义期望的输出格式
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public class OutputFormatComponent extends AbstractPromptComponent {
    
    public OutputFormatComponent(String content) {
        super(PromptComponentType.OUTPUT_FORMAT, content);
    }
    
    public OutputFormatComponent(String content, int priority) {
        super(PromptComponentType.OUTPUT_FORMAT, content, priority);
    }
    
    /**
     * 创建输出格式组件
     * 
     * @param content 输出格式描述
     * @return 输出格式组件实例
     */
    public static OutputFormatComponent of(String content) {
        return new OutputFormatComponent(content);
    }
    
    /**
     * 创建带优先级的输出格式组件
     * 
     * @param content 输出格式描述
     * @param priority 优先级
     * @return 输出格式组件实例
     */
    public static OutputFormatComponent of(String content, int priority) {
        return new OutputFormatComponent(content, priority);
    }
    
    /**
     * 创建JSON格式组件
     * 
     * @return JSON格式组件实例
     */
    public static OutputFormatComponent json() {
        return new OutputFormatComponent("请以JSON格式输出结果");
    }
    
    /**
     * 创建Markdown格式组件
     * 
     * @return Markdown格式组件实例
     */
    public static OutputFormatComponent markdown() {
        return new OutputFormatComponent("请以Markdown格式输出结果");
    }
    
    /**
     * 创建纯文本格式组件
     * 
     * @return 纯文本格式组件实例
     */
    public static OutputFormatComponent plainText() {
        return new OutputFormatComponent("请以纯文本格式输出结果");
    }
    
    /**
     * 创建表格格式组件
     * 
     * @return 表格格式组件实例
     */
    public static OutputFormatComponent table() {
        return new OutputFormatComponent("请以表格格式输出结果");
    }
}
