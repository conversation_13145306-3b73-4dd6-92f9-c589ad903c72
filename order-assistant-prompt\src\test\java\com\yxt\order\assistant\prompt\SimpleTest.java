package com.yxt.order.assistant.prompt;

import com.yxt.order.assistant.prompt.component.OutputFormatComponent;
import com.yxt.order.assistant.prompt.core.PromptBuilder;
import com.yxt.order.assistant.prompt.core.PromptResult;

/**
 * 简单测试类
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public class SimpleTest {
    
    public static void main(String[] args) {
        System.out.println("=== 提示词框架简单测试 ===");
        
        // 测试基础功能
        testBasicFunctionality();
        
        // 测试输出格式组件
        testOutputFormatComponents();
    }
    
    public static void testBasicFunctionality() {
        System.out.println("1. 基础功能测试");
        System.out.println("================");
        
        try {
            PromptResult result = PromptBuilder.create()
                    .withSystem("你是一个专业的AI助手")
                    .withUser("请帮我分析这段代码")
                    .withConstraint("回答要简洁明了")
                    .withOutputFormat("JSON格式")
                    .build();
            
            System.out.println("构建成功: " + result.isSuccess());
            System.out.println("内容长度: " + result.getContent().length());
            System.out.println("组件数量: " + result.getComponents().size());
            System.out.println("内容预览:");
            System.out.println("---");
            System.out.println(result.getContent());
            System.out.println("---");
            
        } catch (Exception e) {
            System.err.println("基础功能测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println();
    }
    
    public static void testOutputFormatComponents() {
        System.out.println("2. 输出格式组件测试");
        System.out.println("==================");
        
        try {
            // 测试JSON格式
            PromptResult jsonResult = PromptBuilder.create()
                    .withSystem("你是一个AI助手")
                    .withUser("测试JSON格式")
                    .withComponent(OutputFormatComponent.json())
                    .build();
            
            System.out.println("JSON格式测试成功: " + jsonResult.isSuccess());
            
            // 测试Markdown格式
            PromptResult markdownResult = PromptBuilder.create()
                    .withSystem("你是一个AI助手")
                    .withUser("测试Markdown格式")
                    .withComponent(OutputFormatComponent.markdown())
                    .build();
            
            System.out.println("Markdown格式测试成功: " + markdownResult.isSuccess());
            
            // 测试纯文本格式
            PromptResult plainTextResult = PromptBuilder.create()
                    .withSystem("你是一个AI助手")
                    .withUser("测试纯文本格式")
                    .withComponent(OutputFormatComponent.plainText())
                    .build();
            
            System.out.println("纯文本格式测试成功: " + plainTextResult.isSuccess());
            
            // 测试表格格式
            PromptResult tableResult = PromptBuilder.create()
                    .withSystem("你是一个AI助手")
                    .withUser("测试表格格式")
                    .withComponent(OutputFormatComponent.table())
                    .build();
            
            System.out.println("表格格式测试成功: " + tableResult.isSuccess());
            
        } catch (Exception e) {
            System.err.println("输出格式组件测试失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println();
    }
}
