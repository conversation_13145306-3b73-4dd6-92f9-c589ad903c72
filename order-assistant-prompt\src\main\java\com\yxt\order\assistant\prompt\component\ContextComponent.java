package com.yxt.order.assistant.prompt.component;

import com.yxt.order.assistant.prompt.core.PromptComponentType;
import com.yxt.order.assistant.prompt.core.PromptContext;

import java.util.Map;

/**
 * 上下文组件
 * 用于提供背景信息和环境设置
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public class ContextComponent extends AbstractPromptComponent {
    
    private final String key;
    private final Object value;
    
    public ContextComponent(String content) {
        super(PromptComponentType.CONTEXT, content);
        this.key = null;
        this.value = null;
    }
    
    public ContextComponent(String key, Object value) {
        super(PromptComponentType.CONTEXT, null);
        this.key = key;
        this.value = value;
    }
    
    public ContextComponent(String content, int priority) {
        super(PromptComponentType.CONTEXT, content, priority);
        this.key = null;
        this.value = null;
    }
    
    @Override
    public String getContent(PromptContext context) {
        if (content != null) {
            return processContent(content, context);
        }
        
        if (key != null && value != null) {
            return formatKeyValue(key, value);
        }
        
        return "";
    }
    
    @Override
    protected String processContent(String rawContent, PromptContext context) {
        // 上下文内容可能包含变量引用，这里可以进行变量替换
        return rawContent;
    }
    
    /**
     * 格式化键值对
     * 
     * @param key 键
     * @param value 值
     * @return 格式化后的字符串
     */
    private String formatKeyValue(String key, Object value) {
        if (value == null) {
            return key + ": (null)";
        }
        
        if (value instanceof Map) {
            StringBuilder sb = new StringBuilder();
            sb.append(key).append(":\n");
            @SuppressWarnings("unchecked")
            Map<String, Object> map = (Map<String, Object>) value;
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                sb.append("  ").append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
            }
            return sb.toString();
        }
        
        return key + ": " + value.toString();
    }
    
    /**
     * 获取上下文键
     * 
     * @return 上下文键
     */
    public String getKey() {
        return key;
    }
    
    /**
     * 获取上下文值
     * 
     * @return 上下文值
     */
    public Object getValue() {
        return value;
    }
    
    /**
     * 创建文本上下文组件
     * 
     * @param content 上下文内容
     * @return 上下文组件实例
     */
    public static ContextComponent of(String content) {
        return new ContextComponent(content);
    }
    
    /**
     * 创建键值对上下文组件
     * 
     * @param key 上下文键
     * @param value 上下文值
     * @return 上下文组件实例
     */
    public static ContextComponent of(String key, Object value) {
        return new ContextComponent(key, value);
    }
    
    /**
     * 创建带优先级的文本上下文组件
     * 
     * @param content 上下文内容
     * @param priority 优先级
     * @return 上下文组件实例
     */
    public static ContextComponent of(String content, int priority) {
        return new ContextComponent(content, priority);
    }
}
