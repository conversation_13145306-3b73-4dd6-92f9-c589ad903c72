package com.yxt.order.assistant.prompt.template;

import com.yxt.order.assistant.prompt.core.PromptTemplate;
import com.yxt.order.assistant.prompt.exception.PromptException;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.RuntimeConstants;
import org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader;
import org.apache.velocity.runtime.resource.loader.StringResourceLoader;

import java.io.StringWriter;
import java.util.Map;
import java.util.Properties;

/**
 * 基于Velocity的提示词模板实现
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public class VelocityPromptTemplate implements PromptTemplate {
    
    private final VelocityEngine velocityEngine;
    private final String templatePath;
    private final String encoding;
    
    public VelocityPromptTemplate() {
        this("templates/", "UTF-8");
    }
    
    public VelocityPromptTemplate(String templatePath, String encoding) {
        this.templatePath = templatePath;
        this.encoding = encoding;
        this.velocityEngine = initVelocityEngine();
    }
    
    /**
     * 初始化Velocity引擎
     * 
     * @return Velocity引擎实例
     */
    private VelocityEngine initVelocityEngine() {
        Properties props = new Properties();
        
        // 设置资源加载器
        props.setProperty(RuntimeConstants.RESOURCE_LOADER, "classpath,string");
        
        // 配置classpath资源加载器
        props.setProperty("classpath.resource.loader.class", ClasspathResourceLoader.class.getName());
        props.setProperty("classpath.resource.loader.cache", "true");
        props.setProperty("classpath.resource.loader.modificationCheckInterval", "2");
        
        // 配置string资源加载器
        props.setProperty("string.resource.loader.class", StringResourceLoader.class.getName());
        props.setProperty("string.resource.loader.repository.static", "false");
        
        // 设置编码
        props.setProperty(RuntimeConstants.INPUT_ENCODING, encoding);
        props.setProperty("output.encoding", encoding);

        // 设置日志
        props.setProperty("runtime.log.logsystem.class",
                "org.apache.velocity.runtime.log.NullLogChute");
        
        VelocityEngine engine = new VelocityEngine();
        engine.init(props);
        return engine;
    }
    
    @Override
    public String render(String templateContent, Map<String, Object> variables) {
        if (templateContent == null || templateContent.trim().isEmpty()) {
            return "";
        }
        
        try {
            VelocityContext context = new VelocityContext();
            if (variables != null) {
                variables.forEach(context::put);
            }
            
            StringWriter writer = new StringWriter();
            velocityEngine.evaluate(context, writer, "StringTemplate", templateContent);
            return writer.toString();
        } catch (Exception e) {
            throw new PromptException("Failed to render template: " + e.getMessage(), e);
        }
    }
    
    @Override
    public String renderTemplate(String templateName, Map<String, Object> variables) {
        if (templateName == null || templateName.trim().isEmpty()) {
            throw new IllegalArgumentException("Template name cannot be null or empty");
        }
        
        try {
            VelocityContext context = new VelocityContext();
            if (variables != null) {
                variables.forEach(context::put);
            }
            
            String templatePath = buildTemplatePath(templateName);
            StringWriter writer = new StringWriter();
            
            if (!velocityEngine.resourceExists(templatePath)) {
                throw new PromptException("Template not found: " + templatePath);
            }
            
            velocityEngine.mergeTemplate(templatePath, encoding, context, writer);
            return writer.toString();
        } catch (Exception e) {
            throw new PromptException("Failed to render template '" + templateName + "': " + e.getMessage(), e);
        }
    }
    
    @Override
    public boolean templateExists(String templateName) {
        if (templateName == null || templateName.trim().isEmpty()) {
            return false;
        }
        
        try {
            String templatePath = buildTemplatePath(templateName);
            return velocityEngine.resourceExists(templatePath);
        } catch (Exception e) {
            return false;
        }
    }
    
    @Override
    public String getEngineName() {
        return "Velocity";
    }
    
    @Override
    public TemplateValidationResult validateTemplate(String templateContent) {
        if (templateContent == null || templateContent.trim().isEmpty()) {
            return TemplateValidationResult.failure("Template content cannot be null or empty");
        }
        
        try {
            // 尝试解析模板
            VelocityContext context = new VelocityContext();
            StringWriter writer = new StringWriter();
            velocityEngine.evaluate(context, writer, "ValidationTemplate", templateContent);
            return TemplateValidationResult.success();
        } catch (Exception e) {
            return TemplateValidationResult.failure("Template validation failed: " + e.getMessage());
        }
    }
    
    /**
     * 构建模板路径
     * 
     * @param templateName 模板名称
     * @return 完整的模板路径
     */
    private String buildTemplatePath(String templateName) {
        if (templateName.startsWith("/")) {
            return templateName.substring(1);
        }
        
        if (templateName.endsWith(".vm")) {
            return templatePath + templateName;
        }
        
        return templatePath + templateName + ".vm";
    }
    
    /**
     * 获取模板路径
     * 
     * @return 模板路径
     */
    public String getTemplatePath() {
        return templatePath;
    }
    
    /**
     * 获取编码
     * 
     * @return 编码
     */
    public String getEncoding() {
        return encoding;
    }
}
