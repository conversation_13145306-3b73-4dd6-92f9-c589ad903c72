package com.yxt.order.assistant.prompt;

import com.yxt.order.assistant.prompt.component.OutputFormatComponent;
import com.yxt.order.assistant.prompt.component.SystemPromptComponent;
import com.yxt.order.assistant.prompt.core.PromptBuilder;
import com.yxt.order.assistant.prompt.core.PromptResult;
import com.yxt.order.assistant.prompt.processor.ValidationProcessor;
import com.yxt.order.assistant.prompt.processor.TemplateProcessor;

/**
 * 提示词框架测试类
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public class PromptFrameworkTest {
    
    public static void main(String[] args) {
//        testBasicUsage();// ok
//        testCodeReviewScenario();// ok
//        testDocumentationScenario();
//        testTemplateUsage(); // ok
        testValidationProcessor();
    }
    
    /**
     * 测试基础使用
     */
    public static void testBasicUsage() {
        System.out.println("=== 基础使用测试 ===");
        
        PromptResult result = PromptBuilder.create()
                .withSystem("你是一个专业的AI助手")
                .withUser("请帮我分析这段代码的问题")
                .withConstraint("回答要简洁明了")
                .withOutputFormat("请以JSON格式输出")
                .build();
        
        System.out.println("构建成功: " + result.isSuccess());
        System.out.println("内容:\n" + result.getContent());
        System.out.println();
    }
    
    /**
     * 测试代码审查场景
     */
    public static void testCodeReviewScenario() {
        System.out.println("=== 代码审查场景测试 ===");
        
        String codeContent = "public class Test { private String name; }";
        
        PromptResult result = PromptBuilder.create()
                .withSystem("你是一个资深的Java开发工程师")
                .withInstruction("请审查以下代码，关注代码质量、性能和安全性：\n\n```java\n${code}\n```")
                .withContext("项目类型", "Spring Boot应用")
                .withContext("编程语言", "Java")
                .withVariable("code", codeContent)
                .withExample(
                    "public class User { private String name; }",
                    "建议：1. 缺少getter/setter方法 2. 建议添加构造函数"
                )
                .withConstraint("输出格式为JSON，包含问题列表和建议")
                .withOutputFormat("JSON")
                .withProcessor(new TemplateProcessor())
                .build();
        
        System.out.println("构建成功: " + result.isSuccess());
        System.out.println("内容:\n" + result.getContent());
        System.out.println();
    }
    
    /**
     * 测试文档生成场景
     */
    public static void testDocumentationScenario() {
        System.out.println("=== 文档生成场景测试 ===");
        
        PromptResult result = PromptBuilder.create()
                .withSystem("你是一个技术文档专家")
                .withInstruction("为以下API生成详细的技术文档：\n\n" +
                        "- **API名称**: ${apiName}\n" +
                        "- **描述**: ${description}\n" +
                        "- **请求方法**: ${method}\n" +
                        "- **请求路径**: ${path}")
                .withVariable("apiName", "getUserInfo")
                .withVariable("description", "获取用户信息")
                .withVariable("method", "GET")
                .withVariable("path", "/api/user/{id}")
                .withOutputFormat("Markdown格式")
                .withConstraint("包含请求参数、响应格式和示例")
                .withProcessor(new TemplateProcessor())
                .build();
        
        System.out.println("构建成功: " + result.isSuccess());
        System.out.println("内容:\n" + result.getContent());
        System.out.println();
    }
    
    /**
     * 测试模板使用
     */
    public static void testTemplateUsage() {
        System.out.println("=== 模板使用测试 ===");
        
        PromptResult result = PromptBuilder.create()
                .withSystem("你是一个${role}")
                .withInstruction("请${action}以下内容：${content}")
                .withVariable("role", "代码审查专家")
                .withVariable("action", "分析")
                .withVariable("content", "Java代码片段")
                .withComponent(OutputFormatComponent.json())
                .withProcessor(new TemplateProcessor())
                .build();
        
        System.out.println("构建成功: " + result.isSuccess());
        System.out.println("内容:\n" + result.getContent());
        System.out.println();
    }
    
    /**
     * 测试验证处理器
     */
    public static void testValidationProcessor() {
        System.out.println("=== 验证处理器测试 ===");
        
        // 测试严格模式 - 直接添加无效的必需组件
        try {
            PromptResult result = PromptBuilder.create()
                    .withComponent(new SystemPromptComponent(""))  // 直接添加空内容的必需组件
                    .withUser("测试用户输入")
                    .withProcessor(new ValidationProcessor(true))  // 严格模式
                    .build();

            System.out.println("严格模式构建成功: " + result.isSuccess());
            if (!result.isSuccess()) {
                System.out.println("错误信息: " + result.getError().map(Exception::getMessage).orElse("未知错误"));
            }
        } catch (Exception e) {
            System.out.println("严格模式验证失败（预期）: " + e.getMessage());
        }
        
        // 测试宽松模式
        PromptResult result = PromptBuilder.create()
                .withSystem("")  // 空的系统提示词
                .withUser("测试用户输入")
                .withProcessor(new ValidationProcessor(false))  // 宽松模式
                .build();
        
        System.out.println("宽松模式构建成功: " + result.isSuccess());
        System.out.println("内容:\n" + result.getContent());
        System.out.println();
    }
}
