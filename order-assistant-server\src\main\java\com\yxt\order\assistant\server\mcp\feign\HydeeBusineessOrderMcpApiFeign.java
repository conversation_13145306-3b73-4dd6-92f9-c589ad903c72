package com.yxt.order.assistant.server.mcp.feign;

import com.yxt.domain.order.BusinessOrderServiceName;
import com.yxt.domain.order.mcp.BusinessOrderMcpApi;
import com.yxt.order.atom.sdk.OrderAtomServiceName;
import com.yxt.order.atom.sdk.mcp.OrderMcpApi;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(value = BusinessOrderServiceName.value)
public interface HydeeBusineessOrderMcpApiFeign extends BusinessOrderMcpApi {

}
