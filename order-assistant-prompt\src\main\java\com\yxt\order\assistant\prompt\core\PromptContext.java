package com.yxt.order.assistant.prompt.core;

import java.util.Map;
import java.util.Optional;

/**
 * 提示词上下文接口
 * 管理提示词构建过程中的变量、配置和状态信息
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public interface PromptContext {
    
    /**
     * 设置变量
     * 
     * @param key 变量名
     * @param value 变量值
     * @return 当前上下文实例，支持链式调用
     */
    PromptContext setVariable(String key, Object value);
    
    /**
     * 批量设置变量
     * 
     * @param variables 变量映射
     * @return 当前上下文实例，支持链式调用
     */
    PromptContext setVariables(Map<String, Object> variables);
    
    /**
     * 获取变量
     * 
     * @param key 变量名
     * @return 变量值的Optional包装
     */
    Optional<Object> getVariable(String key);
    
    /**
     * 获取变量，如果不存在则返回默认值
     * 
     * @param key 变量名
     * @param defaultValue 默认值
     * @param <T> 变量类型
     * @return 变量值或默认值
     */
    <T> T getVariable(String key, T defaultValue);
    
    /**
     * 获取指定类型的变量
     * 
     * @param key 变量名
     * @param type 变量类型
     * @param <T> 变量类型
     * @return 变量值的Optional包装
     */
    <T> Optional<T> getVariable(String key, Class<T> type);
    
    /**
     * 获取所有变量
     * 
     * @return 变量映射的只读视图
     */
    Map<String, Object> getAllVariables();
    
    /**
     * 检查变量是否存在
     * 
     * @param key 变量名
     * @return true表示存在，false表示不存在
     */
    boolean hasVariable(String key);
    
    /**
     * 移除变量
     * 
     * @param key 变量名
     * @return 当前上下文实例，支持链式调用
     */
    PromptContext removeVariable(String key);
    
    /**
     * 清空所有变量
     * 
     * @return 当前上下文实例，支持链式调用
     */
    PromptContext clearVariables();
    
    /**
     * 设置属性
     * 
     * @param key 属性名
     * @param value 属性值
     * @return 当前上下文实例，支持链式调用
     */
    PromptContext setAttribute(String key, Object value);
    
    /**
     * 获取属性
     * 
     * @param key 属性名
     * @return 属性值的Optional包装
     */
    Optional<Object> getAttribute(String key);
    
    /**
     * 获取属性，如果不存在则返回默认值
     * 
     * @param key 属性名
     * @param defaultValue 默认值
     * @param <T> 属性类型
     * @return 属性值或默认值
     */
    <T> T getAttribute(String key, T defaultValue);
    
    /**
     * 创建子上下文
     * 子上下文继承父上下文的变量和属性，但修改不会影响父上下文
     * 
     * @return 子上下文实例
     */
    PromptContext createChild();
    
    /**
     * 获取父上下文
     * 
     * @return 父上下文的Optional包装
     */
    Optional<PromptContext> getParent();
}
