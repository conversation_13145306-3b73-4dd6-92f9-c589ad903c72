package com.yxt.order.assistant.prompt.component;

import com.yxt.order.assistant.prompt.core.PromptComponentType;
import com.yxt.order.assistant.prompt.core.PromptContext;

/**
 * 用户提示词组件
 * 用于包装用户的输入内容
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public class UserPromptComponent extends AbstractPromptComponent {
    
    public UserPromptComponent(String content) {
        super(PromptComponentType.USER, content, true);
    }
    
    public UserPromptComponent(String content, int priority) {
        super(PromptComponentType.USER, content, priority, true);
    }
    
    @Override
    protected String processContent(String rawContent, PromptContext context) {
        // 用户输入通常不需要特殊处理，但可以在这里添加输入清理逻辑
        return rawContent != null ? rawContent.trim() : "";
    }
    
    /**
     * 创建用户提示词组件
     * 
     * @param content 用户输入内容
     * @return 用户提示词组件实例
     */
    public static UserPromptComponent of(String content) {
        return new UserPromptComponent(content);
    }
    
    /**
     * 创建带优先级的用户提示词组件
     * 
     * @param content 用户输入内容
     * @param priority 优先级
     * @return 用户提示词组件实例
     */
    public static UserPromptComponent of(String content, int priority) {
        return new UserPromptComponent(content, priority);
    }
}
