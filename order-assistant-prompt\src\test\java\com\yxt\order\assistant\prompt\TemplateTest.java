package com.yxt.order.assistant.prompt;

import com.yxt.order.assistant.prompt.component.OutputFormatComponent;
import com.yxt.order.assistant.prompt.core.PromptBuilder;
import com.yxt.order.assistant.prompt.core.PromptResult;
import com.yxt.order.assistant.prompt.processor.TemplateProcessor;

/**
 * 模板功能专项测试
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public class TemplateTest {
    
    public static void main(String[] args) {
        System.out.println("=== 模板变量替换测试 ===");
        testTemplateVariableReplacement();
    }
    
    public static void testTemplateVariableReplacement() {
        System.out.println("测试模板变量替换功能...");
        System.out.println();
        
        // 测试基本变量替换
        PromptResult result = PromptBuilder.create()
                .withSystem("你是一个${role}")
                .withInstruction("请${action}以下内容：${content}")
                .withVariable("role", "代码审查专家")
                .withVariable("action", "分析")
                .withVariable("content", "Java代码片段")
                .withComponent(OutputFormatComponent.json())
                .withProcessor(new TemplateProcessor())
                .build();
        
        System.out.println("构建成功: " + result.isSuccess());
        System.out.println("组件数量: " + result.getComponents().size());
        System.out.println();
        System.out.println("生成的提示词内容:");
        System.out.println("==================");
        System.out.println(result.getContent());
        System.out.println("==================");
        System.out.println();
        
        // 验证变量是否被正确替换
        String content = result.getContent();
        boolean roleReplaced = content.contains("代码审查专家") && !content.contains("${role}");
        boolean actionReplaced = content.contains("分析") && !content.contains("${action}");
        boolean contentReplaced = content.contains("Java代码片段") && !content.contains("${content}");
        
        System.out.println("变量替换验证结果:");
        System.out.println("- role变量替换: " + (roleReplaced ? "✓ 成功" : "✗ 失败"));
        System.out.println("- action变量替换: " + (actionReplaced ? "✓ 成功" : "✗ 失败"));
        System.out.println("- content变量替换: " + (contentReplaced ? "✓ 成功" : "✗ 失败"));
        
        boolean allSuccess = roleReplaced && actionReplaced && contentReplaced;
        System.out.println();
        System.out.println("总体结果: " + (allSuccess ? "✓ 所有变量替换成功" : "✗ 部分变量替换失败"));
    }
}
