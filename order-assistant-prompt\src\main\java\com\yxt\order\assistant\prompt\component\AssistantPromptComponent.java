package com.yxt.order.assistant.prompt.component;

import com.yxt.order.assistant.prompt.core.PromptComponentType;

/**
 * 助手提示词组件
 * 用于AI助手的回复，通常用于对话上下文或少样本学习
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public class AssistantPromptComponent extends AbstractPromptComponent {
    
    public AssistantPromptComponent(String content) {
        super(PromptComponentType.ASSISTANT, content);
    }
    
    public AssistantPromptComponent(String content, int priority) {
        super(PromptComponentType.ASSISTANT, content, priority);
    }
    
    /**
     * 创建助手提示词组件
     * 
     * @param content 助手回复内容
     * @return 助手提示词组件实例
     */
    public static AssistantPromptComponent of(String content) {
        return new AssistantPromptComponent(content);
    }
    
    /**
     * 创建带优先级的助手提示词组件
     * 
     * @param content 助手回复内容
     * @param priority 优先级
     * @return 助手提示词组件实例
     */
    public static AssistantPromptComponent of(String content, int priority) {
        return new AssistantPromptComponent(content, priority);
    }
}
