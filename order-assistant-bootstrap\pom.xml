<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.yxt.order</groupId>
    <artifactId>order-assistant</artifactId>
    <version>1.0.0</version>
  </parent>

  <groupId>com.yxt.order.assistant.bootstrap</groupId>
  <artifactId>order-assistant-bootstrap</artifactId>
  <packaging>jar</packaging>

  <properties>
    <maven.compiler.source>21</maven.compiler.source>
    <maven.compiler.target>21</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.yxt</groupId>
      <artifactId>yxt-common-shutdown2</artifactId>
      <version>2.0.1-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.yxt.order.assistant.server</groupId>
      <artifactId>order-assistant-server</artifactId>
      <version>1.0.0</version>
      <exclusions>
        <exclusion>
          <artifactId>commons-logging</artifactId>
          <groupId>commons-logging</groupId>
        </exclusion>
        <exclusion>
          <artifactId>yxt-common-alarm</artifactId>
          <groupId>com.yxt</groupId>
        </exclusion>
        <exclusion>
          <artifactId>yxt-core-spring-boot-starter</artifactId>
          <groupId>com.yxt</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
      <exclusions>
        <exclusion>
          <artifactId>spring-boot-starter-logging</artifactId>
          <groupId>org.springframework.boot</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <!--      <version>4.13.2</version>-->
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-log4j2</artifactId>
    </dependency>
  </dependencies>


  <build>
    <defaultGoal>compile</defaultGoal>
    <finalName>order-assistant</finalName>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <!--打包jar的存放路径-->
      <plugin>
        <artifactId>maven-antrun-plugin</artifactId>
        <executions>
          <execution>
            <id>copy</id>
            <phase>package</phase>
            <configuration>
              <target>
                <delete file="${main.basedir}/target/order-assistant.jar"/>
                <copy todir="${main.basedir}/target">
                  <fileset dir="${project.build.directory}">
                    <include name="order-assistant.jar"/>
                  </fileset>
                </copy>
              </target>
            </configuration>
            <goals>
              <goal>run</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>