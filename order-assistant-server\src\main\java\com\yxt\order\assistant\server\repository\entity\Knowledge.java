package com.yxt.order.assistant.server.repository.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yxt.order.assistant.server.repository.enums.KnowledgeBaseSource;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ToString
@TableName("knowledge")
public class Knowledge {


  @TableId(value = "id", type = IdType.AUTO)
  private Long id;
  /**
   * 知识库来源 CF - confluence文档 Swagger - 接口Swagger
   *
   * @see KnowledgeBaseSource
   */
  private String source;

  /**
   * 目标名称,优先使用程序获取,程序获取不到再人工填写
   */
  private String targetName;

  /**
   * 目标ID source=CF: 表示cf pageId
   */

  private String targetId;

  /**
   * 目标资源状态 NORMAL - 正常 DELETED - 删除
   */

  private String targetStatus;

  /**
   * 知识内容(markdown格式)
   */
  private String content;

  /**
   * 拓展字段，灵活使用，存储JSON
   */
  private String extendJson;

  /**
   * 关联knowledge_base表主键Id
   */
  private Long knowledgeBaseId;


  /**
   * @see com.yxt.order.assistant.server.repository.enums.KnowledgeUploadStatus
   */
  private String uploadStatus;

  /**
   * 映射的远端Id
   */
  private String mappingRemoteId;

  private String error;

}
