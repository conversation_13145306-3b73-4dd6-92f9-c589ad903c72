package com.yxt.order.assistant.prompt.core;

import java.util.List;

/**
 * 提示词处理器接口
 * 用于在提示词构建过程中进行预处理和后处理
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public interface PromptProcessor {
    
    /**
     * 处理提示词组件列表
     * 
     * @param components 组件列表
     * @param context 上下文信息
     * @return 处理后的组件列表
     */
    List<PromptComponent> process(List<PromptComponent> components, PromptContext context);
    
    /**
     * 获取处理器名称
     * 
     * @return 处理器名称
     */
    default String getName() {
        return this.getClass().getSimpleName();
    }
    
    /**
     * 获取处理器优先级
     * 数值越小优先级越高
     * 
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }
    
    /**
     * 检查处理器是否适用于当前上下文
     * 
     * @param context 上下文信息
     * @return true表示适用，false表示不适用
     */
    default boolean isApplicable(PromptContext context) {
        return true;
    }
}
