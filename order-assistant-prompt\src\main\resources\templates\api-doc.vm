# ${apiName} API文档

## 接口描述
${description}

## 请求信息
- **请求方法**: ${method}
- **请求路径**: ${path}
- **Content-Type**: ${contentType}

## 请求参数

### 路径参数
#if($pathParams)
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
#foreach($param in $pathParams)
| ${param.name} | ${param.type} | ${param.required} | ${param.description} |
#end
#else
无路径参数
#end

### 查询参数
#if($queryParams)
| 参数名 | 类型 | 必填 | 描述 | 默认值 |
|--------|------|------|------|-------|
#foreach($param in $queryParams)
| ${param.name} | ${param.type} | ${param.required} | ${param.description} | ${param.defaultValue} |
#end
#else
无查询参数
#end

### 请求体
#if($requestBody)
```json
${requestBody}
```
#else
无请求体
#end

## 响应信息

### 成功响应
- **状态码**: 200
- **响应格式**:
```json
${successResponse}
```

### 错误响应
- **状态码**: ${errorCode}
- **响应格式**:
```json
${errorResponse}
```

## 使用示例

### 请求示例
```bash
curl -X ${method} "${baseUrl}${path}" \
  -H "Content-Type: ${contentType}" \
#if($requestBody)
  -d '${requestBody}'
#end
```

### 响应示例
```json
${responseExample}
```

## 注意事项
#if($notes)
#foreach($note in $notes)
- ${note}
#end
#else
无特殊注意事项
#end
