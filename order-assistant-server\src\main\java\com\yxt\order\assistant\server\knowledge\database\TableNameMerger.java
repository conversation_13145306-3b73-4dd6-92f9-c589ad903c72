package com.yxt.order.assistant.server.knowledge.database;

import com.yxt.order.assistant.server.knowledge.database.dto.DatabaseTableInfo;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.Builder;
import lombok.Data;

public class TableNameMerger {

  public static void main(String[] args) {
    List<String> tableNames = Arrays.asList("extend_data_0", "extend_data_1", "extend_data_2",
        "extend_data_2504", "user_info", "order_2025", "order_2026", "product");

    System.out.println(merge(tableNames));
  }


  /**
   * 合并表名字符串
   *
   * @param tableNames 表名字符串
   * @return 合并后的表名字符串
   */
  public static TableGroup tableNameGroup(List<DatabaseTableInfo> tableNames) {
    Set<DatabaseTableInfo> numericTableNames = new HashSet<>();
    Set<DatabaseTableInfo> commonTableNames = new HashSet<>();

    for (DatabaseTableInfo databaseTableInfo : tableNames) {
      String tableName = databaseTableInfo.getTableName();
      String[] parts = tableName.split("_");
      String lastPart = parts[parts.length - 1];
      if (isNumeric(lastPart) && parts.length > 1) {
        numericTableNames.add(databaseTableInfo);
      } else {
        commonTableNames.add(databaseTableInfo);
      }
    }

    return TableGroup.builder().commonTableNames(commonTableNames)
        .numericTableNames(numericTableNames).build();
  }

  /**
   * 表名按照有数据和没有数字，分别封装
   */
  @Data
  @Builder
  public static class TableGroup {

    private Set<DatabaseTableInfo> numericTableNames;
    private Set<DatabaseTableInfo> commonTableNames;
  }


  /**
   * 合并表名字符串
   *
   * @param tableNames 表名字符串
   * @return 合并后的表名字符串
   */
  public static Set<String> merge(List<String> tableNames) {

    Set<String> mergedTableNames = new HashSet<>();

    for (String tableName : tableNames) {
//      String[] parts = tableName.split("_");
//      String lastPart = parts[parts.length - 1];
//      if (isNumeric(lastPart) && parts.length > 1) {
//        // 去掉最后的数字部分
//        String prefix = String.join("_", Arrays.copyOf(parts, parts.length - 1));
//        mergedTableNames.add(prefix);
//      } else {
//        mergedTableNames.add(tableName);
//      }

      mergedTableNames.add(fetchCleanTable(tableName));

    }

    return mergedTableNames;
  }

  public static String fetchCleanTable(String tableName) {

    String[] parts = tableName.split("_");
    String lastPart = parts[parts.length - 1];
    if (isNumeric(lastPart) && parts.length > 1) {
      // 去掉最后的数字部分
      return String.join("_", Arrays.copyOf(parts, parts.length - 1));

    } else {
      return tableName;
    }
  }

  private static boolean isNumeric(String str) {
    if (str == null || str.isEmpty()) {
      return false;
    }
    for (char c : str.toCharArray()) {
      if (!Character.isDigit(c)) {
        return false;
      }
    }
    return true;
  }
}
