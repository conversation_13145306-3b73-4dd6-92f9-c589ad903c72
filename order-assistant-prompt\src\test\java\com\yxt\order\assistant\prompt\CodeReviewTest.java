package com.yxt.order.assistant.prompt;

import com.yxt.order.assistant.prompt.core.PromptBuilder;
import com.yxt.order.assistant.prompt.core.PromptResult;
import com.yxt.order.assistant.prompt.processor.TemplateProcessor;

/**
 * 代码审查场景专项测试
 * 演示如何在提示词中使用代码变量
 * 
 * <AUTHOR> Assistant
 * @since 1.0.0
 */
public class CodeReviewTest {
    
    public static void main(String[] args) {
        System.out.println("=== 代码审查场景 - 变量使用演示 ===");
        testCodeVariableUsage();
    }
    
    public static void testCodeVariableUsage() {
        // 准备要审查的代码
        String javaCode = "public class UserService {\n" +
                "    private UserRepository userRepository;\n" +
                "    \n" +
                "    public User findById(Long id) {\n" +
                "        return userRepository.findById(id).orElse(null);\n" +
                "    }\n" +
                "}";
        
        System.out.println("原始代码内容:");
        System.out.println("==============");
        System.out.println(javaCode);
        System.out.println("==============");
        System.out.println();
        
        // 构建代码审查提示词
        PromptResult result = PromptBuilder.create()
                .withSystem("你是一个资深的Java开发工程师")
                .withInstruction("请审查以下代码，关注代码质量、性能和安全性：\n\n```java\n${code}\n```")
                .withContext("项目类型", "Spring Boot应用")
                .withContext("编程语言", "Java")
                .withVariable("code", javaCode)  // 这里设置代码变量
                .withExample(
                    "public class User { private String name; }",
                    "建议：1. 缺少getter/setter方法 2. 建议添加构造函数"
                )
                .withConstraint("输出格式为JSON，包含问题列表和建议")
                .withOutputFormat("JSON")
                .withProcessor(new TemplateProcessor())  // 关键：添加模板处理器来替换变量
                .build();
        
        System.out.println("构建结果: " + (result.isSuccess() ? "✓ 成功" : "✗ 失败"));
        System.out.println("组件数量: " + result.getComponents().size());
        System.out.println();
        
        System.out.println("生成的完整提示词:");
        System.out.println("==================");
        System.out.println(result.getContent());
        System.out.println("==================");
        System.out.println();
        
        // 验证代码变量是否被正确替换
        String content = result.getContent();
        boolean codeReplaced = content.contains("public class UserService") && 
                              content.contains("UserRepository userRepository") &&
                              !content.contains("${code}");
        
        System.out.println("变量替换验证:");
        System.out.println("- code变量替换: " + (codeReplaced ? "✓ 成功" : "✗ 失败"));
        System.out.println("- 代码块格式: " + (content.contains("```java") ? "✓ 正确" : "✗ 错误"));
        
        System.out.println();
        System.out.println("总结:");
        System.out.println("withVariable(\"code\", codeContent) 的作用是:");
        System.out.println("1. 将代码内容存储为变量");
        System.out.println("2. 在指令中使用 ${code} 引用这个变量");
        System.out.println("3. 通过 TemplateProcessor 进行变量替换");
        System.out.println("4. 最终在提示词中显示实际的代码内容");
    }
}
