package com.yxt.order.assistant.server.knowledge.confluence;

import java.lang.reflect.Method;

/**
 * 简单的测试类来验证markdown清理功能
 */
public class MarkdownCleanupTest {

    public static void main(String[] args) {
        try {
            ConfluenceMarkdownExporter exporter = new ConfluenceMarkdownExporter();
            
            // 使用反射获取私有方法
            Method cleanupMarkdownMethod = ConfluenceMarkdownExporter.class.getDeclaredMethod("cleanupMarkdown", String.class);
            cleanupMarkdownMethod.setAccessible(true);
            
            Method isHeadingMethod = ConfluenceMarkdownExporter.class.getDeclaredMethod("isHeading", String.class);
            isHeadingMethod.setAccessible(true);
            
            System.out.println("=== 测试标题识别功能 ===");
            testIsHeading(isHeadingMethod, exporter);
            
            System.out.println("\n=== 测试markdown清理功能 ===");
            testCleanupMarkdown(cleanupMarkdownMethod, exporter);
            
            System.out.println("\n✅ 所有测试通过！");
            
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testIsHeading(Method isHeadingMethod, ConfluenceMarkdownExporter exporter) throws Exception {
        // 测试标题识别
        assert (Boolean) isHeadingMethod.invoke(exporter, "# 一级标题") : "应该识别一级标题";
        assert (Boolean) isHeadingMethod.invoke(exporter, "## 二级标题") : "应该识别二级标题";
        assert (Boolean) isHeadingMethod.invoke(exporter, "### 三级标题") : "应该识别三级标题";
        assert (Boolean) isHeadingMethod.invoke(exporter, "#### 四级标题") : "应该识别四级标题";
        assert (Boolean) isHeadingMethod.invoke(exporter, "##### 五级标题") : "应该识别五级标题";
        assert (Boolean) isHeadingMethod.invoke(exporter, "###### 六级标题") : "应该识别六级标题";
        
        // 测试非标题
        assert !(Boolean) isHeadingMethod.invoke(exporter, "普通文本") : "不应该识别普通文本为标题";
        assert !(Boolean) isHeadingMethod.invoke(exporter, "####### 七级标题") : "不应该识别七级标题";
        assert !(Boolean) isHeadingMethod.invoke(exporter, "#没有空格的标题") : "不应该识别没有空格的标题";
        assert !(Boolean) isHeadingMethod.invoke(exporter, "") : "不应该识别空字符串为标题";
        assert !(Boolean) isHeadingMethod.invoke(exporter, null) : "不应该识别null为标题";
        
        System.out.println("✅ 标题识别功能测试通过");
    }
    
    private static void testCleanupMarkdown(Method cleanupMarkdownMethod, ConfluenceMarkdownExporter exporter) throws Exception {
        // 测试1: 移除空行
        String input1 = "# 标题1\n\n\n内容1\n\n\n\n## 标题2\n\n内容2\n\n\n";
        String expected1 = "# 标题1\n内容1\n## 标题2\n内容2";
        String result1 = (String) cleanupMarkdownMethod.invoke(exporter, input1);
        assert expected1.equals(result1) : "移除空行测试失败\n期望: " + expected1 + "\n实际: " + result1;
        System.out.println("✅ 移除空行测试通过");
        
        // 测试2: 移除空标题
        String input2 = "# 标题1\n## 空标题1\n### 空标题2\n## 有内容的标题\n这是内容\n### 另一个空标题\n## 最后的标题\n最后的内容";
        String expected2 = "# 标题1\n## 有内容的标题\n这是内容\n## 最后的标题\n最后的内容";
        String result2 = (String) cleanupMarkdownMethod.invoke(exporter, input2);
        assert expected2.equals(result2) : "移除空标题测试失败\n期望: " + expected2 + "\n实际: " + result2;
        System.out.println("✅ 移除空标题测试通过");
        
        // 测试3: 复杂情况
        String input3 = "# 主标题\n\n\n## 空标题1\n\n\n### 空标题2\n\n\n## 有内容的标题\n\n\n这是一些内容\n\n\n还有更多内容\n\n\n### 子标题\n\n\n子标题内容\n\n\n## 另一个空标题\n\n\n# 最终标题\n\n\n最终内容\n\n\n";
        String expected3 = "# 主标题\n## 有内容的标题\n这是一些内容\n还有更多内容\n### 子标题\n子标题内容\n# 最终标题\n最终内容";
        String result3 = (String) cleanupMarkdownMethod.invoke(exporter, input3);
        assert expected3.equals(result3) : "复杂情况测试失败\n期望: " + expected3 + "\n实际: " + result3;
        System.out.println("✅ 复杂情况测试通过");
        
        // 测试4: 空输入
        assert cleanupMarkdownMethod.invoke(exporter, null) == null : "null输入测试失败";
        assert "".equals(cleanupMarkdownMethod.invoke(exporter, "")) : "空字符串输入测试失败";
        assert "".equals(cleanupMarkdownMethod.invoke(exporter, "   ")) : "空白字符串输入测试失败";
        System.out.println("✅ 空输入测试通过");
        
        // 测试5: 只有标题
        String input5 = "# 标题1\n## 标题2\n### 标题3";
        String expected5 = ""; // 所有标题都没有内容，应该全部被移除
        String result5 = (String) cleanupMarkdownMethod.invoke(exporter, input5);
        assert expected5.equals(result5) : "只有标题测试失败\n期望: '" + expected5 + "'\n实际: '" + result5 + "'";
        System.out.println("✅ 只有标题测试通过");
        
        // 测试6: 只有内容
        String input6 = "这是内容1\n\n\n这是内容2\n\n\n这是内容3\n\n\n";
        String expected6 = "这是内容1\n这是内容2\n这是内容3";
        String result6 = (String) cleanupMarkdownMethod.invoke(exporter, input6);
        assert expected6.equals(result6) : "只有内容测试失败\n期望: " + expected6 + "\n实际: " + result6;
        System.out.println("✅ 只有内容测试通过");
    }
}
